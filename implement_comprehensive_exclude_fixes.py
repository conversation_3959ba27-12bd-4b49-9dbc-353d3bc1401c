#!/usr/bin/env python3
"""
Implement Comprehensive EXCLUDE Fixes
Production-ready fixes for all identified EXCLUDE functionality issues
"""

import sys
import os
import sqlite3
import time

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def implement_comprehensive_exclude_fixes():
    """Implement comprehensive fixes for EXCLUDE functionality issues"""
    print("🔧 IMPLEMENTING COMPREHENSIVE EXCLUDE FIXES")
    print("=" * 60)
    
    fix_results = {
        'database_cleanup': False,
        'pre_reporting_table_cleanup': False,
        'comparison_results_cleanup': False,
        'api_filtering_verification': False,
        'performance_validation': False,
        'end_to_end_testing': False
    }
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return fix_results
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get current session
        sys.path.append(os.path.dirname(__file__))
        from core.session_manager import get_current_session_id
        current_session = get_current_session_id()
        print(f"🎯 Fixing session: {current_session}")
        
        # 1. Database Cleanup - Remove excluded items from existing tables
        print("\n1. 🗄️ DATABASE CLEANUP:")
        
        # Count items before cleanup
        cursor.execute("""
            SELECT COUNT(*) FROM comparison_results cr
            LEFT JOIN dictionary_items di ON cr.item_label = di.item_name
            WHERE cr.session_id = ? AND di.include_in_report = 0
        """, (current_session,))
        
        excluded_comparison_before = cursor.fetchone()[0]
        
        cursor.execute("""
            SELECT COUNT(*) FROM pre_reporting_results pr
            JOIN comparison_results cr ON pr.change_id = cr.id
            LEFT JOIN dictionary_items di ON cr.item_label = di.item_name
            WHERE cr.session_id = ? AND di.include_in_report = 0
        """, (current_session,))
        
        excluded_pre_reporting_before = cursor.fetchone()[0]
        
        print(f"   📊 Before cleanup:")
        print(f"     - Excluded items in comparison_results: {excluded_comparison_before}")
        print(f"     - Excluded items in pre_reporting_results: {excluded_pre_reporting_before}")
        
        if excluded_comparison_before > 0 or excluded_pre_reporting_before > 0:
            print("   🔧 Performing database cleanup...")
            
            # Clean up pre_reporting_results first (due to foreign key constraints)
            cursor.execute("""
                DELETE FROM pre_reporting_results 
                WHERE change_id IN (
                    SELECT cr.id FROM comparison_results cr
                    LEFT JOIN dictionary_items di ON cr.item_label = di.item_name
                    WHERE cr.session_id = ? AND di.include_in_report = 0
                )
            """, (current_session,))
            
            pre_reporting_deleted = cursor.rowcount
            
            # Clean up comparison_results
            cursor.execute("""
                DELETE FROM comparison_results 
                WHERE id IN (
                    SELECT cr.id FROM comparison_results cr
                    LEFT JOIN dictionary_items di ON cr.item_label = di.item_name
                    WHERE cr.session_id = ? AND di.include_in_report = 0
                )
            """, (current_session,))
            
            comparison_deleted = cursor.rowcount
            
            conn.commit()
            
            print(f"   ✅ Cleanup completed:")
            print(f"     - Deleted {pre_reporting_deleted} pre_reporting_results entries")
            print(f"     - Deleted {comparison_deleted} comparison_results entries")
            
            fix_results['database_cleanup'] = True
        else:
            print("   ✅ No excluded items found in database tables")
            fix_results['database_cleanup'] = True
        
        # 2. Pre-reporting Table Cleanup Verification
        print("\n2. 📋 PRE-REPORTING TABLE CLEANUP VERIFICATION:")
        
        cursor.execute("""
            SELECT COUNT(*) FROM pre_reporting_results pr
            JOIN comparison_results cr ON pr.change_id = cr.id
            LEFT JOIN dictionary_items di ON cr.item_label = di.item_name
            WHERE cr.session_id = ? AND di.include_in_report = 0
        """, (current_session,))
        
        excluded_pre_reporting_after = cursor.fetchone()[0]
        
        if excluded_pre_reporting_after == 0:
            print("   ✅ Pre-reporting table cleanup successful")
            fix_results['pre_reporting_table_cleanup'] = True
        else:
            print(f"   ❌ {excluded_pre_reporting_after} excluded items still in pre_reporting_results")
        
        # 3. Comparison Results Cleanup Verification
        print("\n3. ⚖️ COMPARISON RESULTS CLEANUP VERIFICATION:")
        
        cursor.execute("""
            SELECT COUNT(*) FROM comparison_results cr
            LEFT JOIN dictionary_items di ON cr.item_label = di.item_name
            WHERE cr.session_id = ? AND di.include_in_report = 0
        """, (current_session,))
        
        excluded_comparison_after = cursor.fetchone()[0]
        
        if excluded_comparison_after == 0:
            print("   ✅ Comparison results cleanup successful")
            fix_results['comparison_results_cleanup'] = True
        else:
            print(f"   ❌ {excluded_comparison_after} excluded items still in comparison_results")
        
        # 4. API Filtering Verification
        print("\n4. 🔄 API FILTERING VERIFICATION:")
        
        try:
            from core.phased_process_manager import PhasedProcessManager
            
            manager = PhasedProcessManager(debug_mode=False)
            
            # Test get_pre_reporting_data method
            result = manager.get_pre_reporting_data(current_session)
            
            if result.get('success'):
                data = result.get('data', [])
                print(f"   📊 Pre-reporting API returned: {len(data)} items")
                
                # Create test exclusions to verify filtering
                cursor.execute("""
                    SELECT DISTINCT cr.section_name, cr.item_label 
                    FROM comparison_results cr
                    WHERE cr.session_id = ?
                    LIMIT 2
                """, (current_session,))
                
                test_items = cursor.fetchall()
                
                if test_items:
                    # Temporarily exclude items for testing
                    for section, item in test_items:
                        cursor.execute("""
                            INSERT OR IGNORE INTO dictionary_sections (section_name) 
                            VALUES (?)
                        """, (section,))
                        
                        cursor.execute("""
                            SELECT id FROM dictionary_sections WHERE section_name = ?
                        """, (section,))
                        section_id = cursor.fetchone()[0]
                        
                        cursor.execute("""
                            INSERT OR REPLACE INTO dictionary_items 
                            (item_name, section_id, include_in_report) 
                            VALUES (?, ?, 0)
                        """, (item, section_id))
                    
                    conn.commit()
                    
                    # Test API filtering
                    result_filtered = manager.get_pre_reporting_data(current_session)
                    
                    if result_filtered.get('success'):
                        filtered_data = result_filtered.get('data', [])
                        print(f"   📊 After exclusions: {len(filtered_data)} items")
                        
                        # Check if excluded items are present
                        excluded_found = 0
                        for item in filtered_data:
                            for section, excluded_item in test_items:
                                if (item.get('section_name') == section and 
                                    item.get('item_label') == excluded_item):
                                    excluded_found += 1
                        
                        if excluded_found == 0:
                            print("   ✅ API filtering working correctly")
                            fix_results['api_filtering_verification'] = True
                        else:
                            print(f"   ❌ {excluded_found} excluded items found in API results")
                    
                    # Restore items
                    for section, item in test_items:
                        cursor.execute("""
                            UPDATE dictionary_items 
                            SET include_in_report = 1 
                            WHERE item_name = ?
                        """, (item,))
                    conn.commit()
                else:
                    print("   ✅ API filtering verification completed (no test items)")
                    fix_results['api_filtering_verification'] = True
            else:
                print(f"   ❌ API filtering test failed: {result.get('error')}")
                
        except Exception as e:
            print(f"   ❌ API filtering verification error: {e}")
        
        # 5. Performance Validation
        print("\n5. ⚡ PERFORMANCE VALIDATION:")
        
        # Test query performance
        start_time = time.time()
        cursor.execute("""
            SELECT COUNT(*) FROM comparison_results 
            WHERE session_id = ?
        """, (current_session,))
        unfiltered_count = cursor.fetchone()[0]
        unfiltered_time = time.time() - start_time
        
        start_time = time.time()
        cursor.execute("""
            SELECT COUNT(*) FROM comparison_results cr
            LEFT JOIN dictionary_items di ON cr.item_label = di.item_name
            WHERE cr.session_id = ? 
              AND (di.include_in_report = 1 OR di.include_in_report IS NULL)
        """, (current_session,))
        filtered_count = cursor.fetchone()[0]
        filtered_time = time.time() - start_time
        
        reduction_percentage = ((unfiltered_count - filtered_count) / unfiltered_count) * 100 if unfiltered_count > 0 else 0
        performance_ratio = filtered_time / unfiltered_time if unfiltered_time > 0 else 1
        
        print(f"   📊 Unfiltered: {unfiltered_count} items in {unfiltered_time:.4f}s")
        print(f"   📊 Filtered: {filtered_count} items in {filtered_time:.4f}s")
        print(f"   📊 Data reduction: {reduction_percentage:.1f}%")
        print(f"   📊 Performance ratio: {performance_ratio:.2f}x")
        
        if performance_ratio < 3.0:  # Less than 3x slower is acceptable
            print("   ✅ Performance validation passed")
            fix_results['performance_validation'] = True
        else:
            print("   ❌ Performance validation failed")
        
        # 6. End-to-End Testing
        print("\n6. 🔄 END-TO-END TESTING:")
        
        try:
            from core.advanced_reporting_system import AdvancedReportingSystem
            
            # Test report generation filtering
            reporting_system = AdvancedReportingSystem()
            
            test_data = {
                'comparison_results': [
                    {
                        'section_name': 'EARNINGS',
                        'item_label': 'BASIC SALARY',
                        'employee_id': 'TEST001',
                        'value': '1000'
                    },
                    {
                        'section_name': 'DEDUCTIONS',
                        'item_label': 'INCOME TAX',
                        'employee_id': 'TEST002',
                        'value': '100'
                    }
                ]
            }
            
            # Temporarily exclude one item
            cursor.execute("""
                INSERT OR IGNORE INTO dictionary_sections (section_name) 
                VALUES ('DEDUCTIONS')
            """)
            
            cursor.execute("""
                SELECT id FROM dictionary_sections WHERE section_name = 'DEDUCTIONS'
            """)
            section_id = cursor.fetchone()[0]
            
            cursor.execute("""
                INSERT OR REPLACE INTO dictionary_items 
                (item_name, section_id, include_in_report) 
                VALUES ('INCOME TAX', ?, 0)
            """, (section_id,))
            
            conn.commit()
            
            # Test filtering
            filtered_data = reporting_system._apply_include_in_report_filter(test_data)
            
            # Check results
            excluded_found = 0
            for item in filtered_data['comparison_results']:
                if item.get('item_label') == 'INCOME TAX':
                    excluded_found += 1
            
            if excluded_found == 0:
                print("   ✅ End-to-end testing passed")
                fix_results['end_to_end_testing'] = True
            else:
                print(f"   ❌ End-to-end testing failed: {excluded_found} excluded items found")
            
            # Restore item
            cursor.execute("""
                UPDATE dictionary_items 
                SET include_in_report = 1 
                WHERE item_name = 'INCOME TAX'
            """)
            conn.commit()
            
        except Exception as e:
            print(f"   ❌ End-to-end testing error: {e}")
        
        conn.close()
        
        # Summary
        print("\n7. 📋 COMPREHENSIVE FIXES SUMMARY:")
        print("   " + "=" * 50)
        
        passed_fixes = sum(fix_results.values())
        total_fixes = len(fix_results)
        
        for fix_name, result in fix_results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"   {fix_name.replace('_', ' ').title()}: {status}")
        
        print("   " + "=" * 50)
        print(f"   OVERALL RESULT: {passed_fixes}/{total_fixes} fixes successful")
        
        if passed_fixes == total_fixes:
            print("   🎯 EXCLUDE FUNCTIONALITY: FULLY OPERATIONAL ✅")
            print("   🚀 ALL ISSUES RESOLVED - PRODUCTION READY")
        elif passed_fixes >= total_fixes * 0.8:
            print("   ⚠️ EXCLUDE FUNCTIONALITY: MOSTLY OPERATIONAL")
            print("   🔧 MINOR ISSUES REMAINING")
        else:
            print("   ❌ EXCLUDE FUNCTIONALITY: CRITICAL ISSUES REMAIN")
            print("   🚨 ADDITIONAL FIXES REQUIRED")
        
        return fix_results
        
    except Exception as e:
        print(f"❌ Critical error during comprehensive fixes: {e}")
        import traceback
        traceback.print_exc()
        return fix_results

if __name__ == "__main__":
    results = implement_comprehensive_exclude_fixes()
    
    # Exit with appropriate code
    passed_fixes = sum(results.values())
    total_fixes = len(results)
    
    if passed_fixes == total_fixes:
        sys.exit(0)  # All fixes successful
    else:
        sys.exit(1)  # Some fixes failed
