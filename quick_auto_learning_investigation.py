#!/usr/bin/env python3
"""
Quick Auto-Learning System Investigation
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def quick_investigation():
    """Quick investigation of Auto-Learning system"""
    print("🔍 QUICK AUTO-LEARNING INVESTIGATION")
    print("=" * 50)
    
    try:
        # 1. Check database
        db_path = get_database_path()
        if not db_path:
            print("❌ Database not found")
            return
        
        print(f"✅ Database found: {db_path}")
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 2. Check current session
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.session_manager import get_current_session_id
            current_session = get_current_session_id()
            print(f"📋 Current session: {current_session}")
        except Exception as e:
            print(f"❌ Session manager error: {e}")
            # Get latest session from database
            cursor.execute("SELECT DISTINCT session_id FROM comparison_results ORDER BY session_id DESC LIMIT 1")
            result = cursor.fetchone()
            current_session = result[0] if result else None
            print(f"📋 Latest session from DB: {current_session}")
        
        # 3. Check auto_learning_items table
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='auto_learning_items'")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print("✅ auto_learning_items table exists")
            
            # Check total items
            cursor.execute("SELECT COUNT(*) FROM auto_learning_items")
            total_items = cursor.fetchone()[0]
            print(f"📊 Total auto-learning items: {total_items}")
            
            if current_session:
                # Check items for current session
                cursor.execute("SELECT COUNT(*) FROM auto_learning_items WHERE session_id = ?", (current_session,))
                session_items = cursor.fetchone()[0]
                print(f"📊 Items for current session: {session_items}")
                
                # Check pending items for current session
                cursor.execute("SELECT COUNT(*) FROM auto_learning_items WHERE session_id = ? AND status = 'pending'", (current_session,))
                pending_items = cursor.fetchone()[0]
                print(f"📊 Pending items for current session: {pending_items}")
                
                if pending_items > 0:
                    # Show some pending items
                    cursor.execute("""
                        SELECT item_name, suggested_section, confidence_score, created_at
                        FROM auto_learning_items 
                        WHERE session_id = ? AND status = 'pending'
                        LIMIT 5
                    """, (current_session,))
                    
                    items = cursor.fetchall()
                    print("📋 Sample pending items:")
                    for item_name, section, confidence, created_at in items:
                        print(f"   {item_name} → {section} (confidence: {confidence})")
        else:
            print("❌ auto_learning_items table does not exist")
        
        # 4. Check comparison results
        if current_session:
            cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (current_session,))
            comparison_count = cursor.fetchone()[0]
            print(f"📊 Comparison results for session: {comparison_count}")
            
            # Check NEW items
            cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ? AND change_type = 'NEW'", (current_session,))
            new_items = cursor.fetchone()[0]
            print(f"📊 NEW items in comparison results: {new_items}")
        
        # 5. Test Auto-Learning system
        print("\n🤖 TESTING AUTO-LEARNING SYSTEM:")
        
        try:
            from backup_created_files.enhanced_dictionary_auto_learning import EnhancedDictionaryAutoLearning
            
            auto_learning = EnhancedDictionaryAutoLearning(debug=True)
            print("✅ Auto-Learning system imported successfully")
            
            # Test getting pending items
            pending_items = auto_learning.get_pending_items()
            print(f"📊 Pending items from API: {len(pending_items)}")
            
            if len(pending_items) > 0:
                print("📋 Sample items from API:")
                for i, item in enumerate(pending_items[:3]):
                    print(f"   {i+1}. {item}")
            
        except Exception as e:
            print(f"❌ Auto-Learning system test failed: {e}")
            import traceback
            traceback.print_exc()
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Investigation failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    quick_investigation()
