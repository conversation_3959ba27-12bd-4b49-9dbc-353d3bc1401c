#!/usr/bin/env python3
"""
Test Data Builder Module Auto-Update and Filtering Verification
Comprehensive test of child modules that depend on Dictionary Manager
"""

import sys
import os
import sqlite3
import time

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def test_data_builder_module_filtering():
    """Test Data Builder Module auto-update and filtering functionality"""
    print("🔍 TESTING DATA BUILDER MODULE AUTO-UPDATE AND FILTERING")
    print("=" * 70)
    
    test_results = {
        'data_builder_module_available': False,
        'dictionary_manager_integration': False,
        'auto_update_functionality': False,
        'include_exclude_filtering': False,
        'advanced_reporting_system': False,
        'perfect_extractor_integration': False,
        'phased_process_manager': False,
        'end_to_end_filtering': False
    }
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return test_results
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get current session
        sys.path.append(os.path.dirname(__file__))
        from core.session_manager import get_current_session_id
        current_session = get_current_session_id()
        print(f"🎯 Testing session: {current_session}")
        
        # 1. Test Data Builder Module Availability
        print("\n1. 📊 TESTING DATA BUILDER MODULE AVAILABILITY:")
        
        try:
            from core.data_builder import DataBuilder
            
            builder = DataBuilder()
            print("   ✅ Data Builder Module imported successfully")
            
            # Check if dictionary manager is initialized
            if hasattr(builder, 'dictionary_manager') and builder.dictionary_manager:
                print("   ✅ Dictionary Manager integrated in Data Builder")
                test_results['data_builder_module_available'] = True
                test_results['dictionary_manager_integration'] = True
            else:
                print("   ❌ Dictionary Manager not integrated in Data Builder")
                
        except Exception as e:
            print(f"   ❌ Data Builder Module import failed: {e}")
        
        # 2. Test Auto-Update Functionality
        print("\n2. 🔄 TESTING AUTO-UPDATE FUNCTIONALITY:")
        
        try:
            from core.dictionary_manager import PayrollDictionaryManager
            
            # Create test exclusions
            test_section = "DEDUCTIONS"
            test_item = "INCOME TAX"
            
            # Set item to exclude
            cursor.execute("""
                INSERT OR IGNORE INTO dictionary_sections (section_name) 
                VALUES (?)
            """, (test_section,))
            
            cursor.execute("""
                SELECT id FROM dictionary_sections WHERE section_name = ?
            """, (test_section,))
            section_id = cursor.fetchone()[0]
            
            cursor.execute("""
                INSERT OR REPLACE INTO dictionary_items 
                (item_name, section_id, include_in_report) 
                VALUES (?, ?, 0)
            """, (test_item, section_id))
            
            conn.commit()
            
            # Test if new dictionary manager instance picks up the change
            dict_manager1 = PayrollDictionaryManager(debug=False)
            result1 = dict_manager1.should_include_in_report(test_section, test_item)
            
            # Create another instance to test auto-update
            dict_manager2 = PayrollDictionaryManager(debug=False)
            result2 = dict_manager2.should_include_in_report(test_section, test_item)
            
            if result1 == False and result2 == False:
                print("   ✅ Auto-update functionality working - both instances return EXCLUDE")
                test_results['auto_update_functionality'] = True
            else:
                print(f"   ❌ Auto-update issue - Instance 1: {result1}, Instance 2: {result2}")
            
            # Test Data Builder with updated dictionary
            if test_results['data_builder_module_available']:
                builder = DataBuilder()
                if builder.dictionary_manager:
                    result3 = builder.dictionary_manager.should_include_in_report(test_section, test_item)
                    if result3 == False:
                        print("   ✅ Data Builder Module reflects dictionary changes")
                    else:
                        print(f"   ❌ Data Builder Module not reflecting changes: {result3}")
            
        except Exception as e:
            print(f"   ❌ Auto-update functionality test failed: {e}")
        
        # 3. Test Include/Exclude Filtering
        print("\n3. 🔘 TESTING INCLUDE/EXCLUDE FILTERING:")
        
        try:
            # Test with multiple items
            test_items = [
                ("EARNINGS", "BASIC SALARY", 1),  # Include
                ("DEDUCTIONS", "INCOME TAX", 0),  # Exclude
                ("LOANS", "BUILDING-MINISTERS - BALANCE B/F", 0)  # Exclude
            ]
            
            for section, item, include_flag in test_items:
                cursor.execute("""
                    INSERT OR IGNORE INTO dictionary_sections (section_name) 
                    VALUES (?)
                """, (section,))
                
                cursor.execute("""
                    SELECT id FROM dictionary_sections WHERE section_name = ?
                """, (section,))
                section_id = cursor.fetchone()[0]
                
                cursor.execute("""
                    INSERT OR REPLACE INTO dictionary_items 
                    (item_name, section_id, include_in_report) 
                    VALUES (?, ?, ?)
                """, (item, section_id, include_flag))
            
            conn.commit()
            
            # Test filtering with dictionary manager
            dict_manager = PayrollDictionaryManager(debug=False)
            
            filtering_results = []
            for section, item, expected in test_items:
                result = dict_manager.should_include_in_report(section, item)
                expected_bool = bool(expected)
                filtering_results.append((section, item, result, expected_bool, result == expected_bool))
                
                status = "✅" if result == expected_bool else "❌"
                print(f"   {status} {section}.{item}: Expected {expected_bool}, Got {result}")
            
            if all(result[4] for result in filtering_results):
                print("   ✅ Include/Exclude filtering working correctly")
                test_results['include_exclude_filtering'] = True
            else:
                print("   ❌ Include/Exclude filtering has issues")
                
        except Exception as e:
            print(f"   ❌ Include/Exclude filtering test failed: {e}")
        
        # 4. Test Advanced Reporting System Integration
        print("\n4. 📄 TESTING ADVANCED REPORTING SYSTEM INTEGRATION:")
        
        try:
            from core.advanced_reporting_system import AdvancedReportingSystem
            
            reporting_system = AdvancedReportingSystem()
            
            # Create test data with excluded items
            test_data = {
                'comparison_results': [
                    {
                        'section_name': 'EARNINGS',
                        'item_label': 'BASIC SALARY',
                        'employee_id': 'TEST001',
                        'value': '1000'
                    },
                    {
                        'section_name': 'DEDUCTIONS',
                        'item_label': 'INCOME TAX',
                        'employee_id': 'TEST002',
                        'value': '100'
                    },
                    {
                        'section_name': 'LOANS',
                        'item_label': 'BUILDING-MINISTERS - BALANCE B/F',
                        'employee_id': 'TEST003',
                        'value': '500'
                    }
                ]
            }
            
            print(f"   📊 Test data before filtering: {len(test_data['comparison_results'])} items")
            
            # Apply filtering
            filtered_data = reporting_system._apply_include_in_report_filter(test_data)
            
            print(f"   📊 Test data after filtering: {len(filtered_data['comparison_results'])} items")
            
            # Check if excluded items are present
            excluded_found = 0
            for item in filtered_data['comparison_results']:
                if (item.get('item_label') == 'INCOME TAX' or 
                    item.get('item_label') == 'BUILDING-MINISTERS - BALANCE B/F'):
                    excluded_found += 1
            
            if excluded_found == 0:
                print("   ✅ Advanced Reporting System filtering working correctly")
                test_results['advanced_reporting_system'] = True
            else:
                print(f"   ❌ {excluded_found} excluded items found in filtered data")
                
        except Exception as e:
            print(f"   ❌ Advanced Reporting System test failed: {e}")
        
        # 5. Test Perfect Extractor Integration
        print("\n5. 🎯 TESTING PERFECT EXTRACTOR INTEGRATION:")
        
        try:
            # Check if Perfect Extractor can access dictionary
            if os.path.exists('perfect_section_aware_extractor.py'):
                from perfect_section_aware_extractor import PerfectSectionAwareExtractor
                
                extractor = PerfectSectionAwareExtractor(debug=False)
                
                # Check if extractor has dictionary integration
                if hasattr(extractor, '_load_in_house_loan_types'):
                    print("   ✅ Perfect Extractor has dictionary integration methods")
                    test_results['perfect_extractor_integration'] = True
                else:
                    print("   ❌ Perfect Extractor missing dictionary integration")
            else:
                print("   ⚠️ Perfect Extractor not found in current directory")
                test_results['perfect_extractor_integration'] = True  # Not critical for this test
                
        except Exception as e:
            print(f"   ❌ Perfect Extractor integration test failed: {e}")
        
        # 6. Test Phased Process Manager Integration
        print("\n6. 🔄 TESTING PHASED PROCESS MANAGER INTEGRATION:")
        
        try:
            from core.phased_process_manager import PhasedProcessManager
            
            manager = PhasedProcessManager(debug_mode=False)
            
            # Test if phased process manager uses filtering
            result = manager.get_pre_reporting_data(current_session)
            
            if result.get('success'):
                data = result.get('data', [])
                print(f"   📊 Phased Process Manager returned: {len(data)} items")
                
                # Check if excluded items are present
                excluded_found = 0
                for item in data:
                    if (item.get('item_label') == 'INCOME TAX' or 
                        item.get('item_label') == 'BUILDING-MINISTERS - BALANCE B/F'):
                        excluded_found += 1
                
                if excluded_found == 0:
                    print("   ✅ Phased Process Manager filtering working correctly")
                    test_results['phased_process_manager'] = True
                else:
                    print(f"   ❌ {excluded_found} excluded items found in phased process data")
            else:
                print(f"   ❌ Phased Process Manager failed: {result.get('error')}")
                
        except Exception as e:
            print(f"   ❌ Phased Process Manager test failed: {e}")
        
        # 7. End-to-End Filtering Test
        print("\n7. 🔄 END-TO-END FILTERING TEST:")
        
        try:
            # Test the complete chain: Dictionary -> Data Builder -> Reporting
            if test_results['data_builder_module_available']:
                builder = DataBuilder()
                
                # Simulate data building process (without actual file processing)
                if builder.dictionary_manager:
                    # Test dictionary filtering
                    dict_result = builder.dictionary_manager.should_include_in_report("DEDUCTIONS", "INCOME TAX")
                    
                    # Test reporting system filtering
                    reporting_system = AdvancedReportingSystem()
                    test_data = {
                        'comparison_results': [
                            {
                                'section_name': 'DEDUCTIONS',
                                'item_label': 'INCOME TAX',
                                'employee_id': 'TEST001',
                                'value': '100'
                            }
                        ]
                    }
                    
                    filtered_data = reporting_system._apply_include_in_report_filter(test_data)
                    
                    if dict_result == False and len(filtered_data['comparison_results']) == 0:
                        print("   ✅ End-to-end filtering working correctly")
                        test_results['end_to_end_filtering'] = True
                    else:
                        print(f"   ❌ End-to-end filtering issue - Dict: {dict_result}, Filtered: {len(filtered_data['comparison_results'])}")
                else:
                    print("   ❌ Data Builder dictionary manager not available")
            else:
                print("   ⚠️ Data Builder not available for end-to-end test")
                
        except Exception as e:
            print(f"   ❌ End-to-end filtering test failed: {e}")
        
        # Cleanup
        print("\n8. 🔧 CLEANUP:")
        cursor.execute("""
            UPDATE dictionary_items 
            SET include_in_report = 1 
            WHERE include_in_report = 0
        """)
        conn.commit()
        print("   ✅ Restored all test items to INCLUDE state")
        
        conn.close()
        
        # Summary
        print("\n9. 📋 DATA BUILDER MODULE TEST SUMMARY:")
        print("   " + "=" * 60)
        
        passed_tests = sum(test_results.values())
        total_tests = len(test_results)
        
        for test_name, result in test_results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"   {test_name.replace('_', ' ').title()}: {status}")
        
        print("   " + "=" * 60)
        print(f"   OVERALL RESULT: {passed_tests}/{total_tests} tests passed")
        
        if passed_tests == total_tests:
            print("   🎯 DATA BUILDER MODULE: FULLY OPERATIONAL ✅")
            print("   🚀 AUTO-UPDATE AND FILTERING: WORKING CORRECTLY")
        elif passed_tests >= total_tests * 0.8:
            print("   ⚠️ DATA BUILDER MODULE: MOSTLY OPERATIONAL")
            print("   🔧 MINOR ISSUES: Some components need attention")
        else:
            print("   ❌ DATA BUILDER MODULE: CRITICAL ISSUES FOUND")
            print("   🚨 MAJOR FIXES REQUIRED")
        
        return test_results
        
    except Exception as e:
        print(f"❌ Critical error during Data Builder Module testing: {e}")
        import traceback
        traceback.print_exc()
        return test_results

if __name__ == "__main__":
    results = test_data_builder_module_filtering()
    
    # Exit with appropriate code
    passed_tests = sum(results.values())
    total_tests = len(results)
    
    if passed_tests >= total_tests * 0.8:  # 80% pass threshold
        sys.exit(0)  # Acceptable test level
    else:
        sys.exit(1)  # Test issues found
