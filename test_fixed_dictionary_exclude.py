#!/usr/bin/env python3
"""
Test Fixed Dictionary EXCLUDE Functionality
Quick test to verify the dictionary manager fix
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def test_fixed_dictionary_exclude():
    """Test the fixed dictionary exclude functionality"""
    print("🔍 TESTING FIXED DICTIONARY EXCLUDE FUNCTIONALITY")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Setup test exclusion
        print("\n1. 🔧 SETTING UP TEST EXCLUSION:")
        
        test_section = "DEDUCTIONS"
        test_item = "INCOME TAX"
        
        # Set item to exclude
        cursor.execute("""
            UPDATE dictionary_items 
            SET include_in_report = 0 
            WHERE item_name = ? AND section_id = (
                SELECT id FROM dictionary_sections WHERE section_name = ?
            )
        """, (test_item, test_section))
        
        if cursor.rowcount > 0:
            print(f"   ✅ Set {test_section}.{test_item} to EXCLUDE")
        else:
            # Insert if doesn't exist
            cursor.execute("""
                INSERT OR IGNORE INTO dictionary_sections (section_name) 
                VALUES (?)
            """, (test_section,))
            
            cursor.execute("""
                SELECT id FROM dictionary_sections WHERE section_name = ?
            """, (test_section,))
            section_id = cursor.fetchone()[0]
            
            cursor.execute("""
                INSERT INTO dictionary_items (item_name, section_id, include_in_report) 
                VALUES (?, ?, 0)
            """, (test_item, section_id))
            
            print(f"   ✅ Created {test_section}.{test_item} as EXCLUDE")
        
        conn.commit()
        
        # Test dictionary manager
        print("\n2. 🔘 TESTING DICTIONARY MANAGER:")
        
        sys.path.append(os.path.dirname(__file__))
        from core.dictionary_manager import PayrollDictionaryManager
        
        manager = PayrollDictionaryManager(debug=True)
        
        # Test the excluded item
        result = manager.should_include_in_report(test_section, test_item)
        print(f"   {test_section}.{test_item}: {'INCLUDE' if result else 'EXCLUDE'}")
        
        if result == False:
            print("   ✅ Dictionary manager correctly returns EXCLUDE")
            test_passed = True
        else:
            print("   ❌ Dictionary manager should return EXCLUDE but returned INCLUDE")
            test_passed = False
        
        # Test an included item
        cursor.execute("""
            SELECT di.item_name, ds.section_name
            FROM dictionary_items di
            JOIN dictionary_sections ds ON di.section_id = ds.id
            WHERE di.include_in_report = 1
            LIMIT 1
        """)
        
        included_result = cursor.fetchone()
        if included_result:
            included_item, included_section = included_result
            result2 = manager.should_include_in_report(included_section, included_item)
            print(f"   {included_section}.{included_item}: {'INCLUDE' if result2 else 'EXCLUDE'}")
            
            if result2 == True:
                print("   ✅ Dictionary manager correctly returns INCLUDE for included item")
            else:
                print("   ❌ Dictionary manager should return INCLUDE for included item")
                test_passed = False
        
        # Test report filtering
        print("\n3. 📄 TESTING REPORT FILTERING:")
        
        from core.advanced_reporting_system import AdvancedReportingSystem
        
        reporting_system = AdvancedReportingSystem()
        
        test_data = {
            'comparison_results': [
                {
                    'section_name': test_section,
                    'item_label': test_item,
                    'employee_id': 'TEST001',
                    'value': '100'
                },
                {
                    'section_name': 'EARNINGS',
                    'item_label': 'BASIC SALARY',
                    'employee_id': 'TEST002',
                    'value': '1000'
                }
            ]
        }
        
        print(f"   Before filtering: {len(test_data['comparison_results'])} items")
        
        filtered_data = reporting_system._apply_include_in_report_filter(test_data)
        
        print(f"   After filtering: {len(filtered_data['comparison_results'])} items")
        
        # Check if excluded item is present
        excluded_found = False
        for item in filtered_data['comparison_results']:
            if item['section_name'] == test_section and item['item_label'] == test_item:
                excluded_found = True
                break
        
        if not excluded_found:
            print("   ✅ Report filtering correctly excludes the item")
        else:
            print("   ❌ Report filtering should exclude the item but it's still present")
            test_passed = False
        
        # Cleanup
        print("\n4. 🔧 CLEANUP:")
        cursor.execute("""
            UPDATE dictionary_items 
            SET include_in_report = 1 
            WHERE item_name = ?
        """, (test_item,))
        conn.commit()
        print(f"   ✅ Restored {test_section}.{test_item} to INCLUDE")
        
        conn.close()
        
        print(f"\n5. 📋 TEST RESULT:")
        if test_passed:
            print("   🎯 EXCLUDE functionality: WORKING CORRECTLY ✅")
        else:
            print("   ❌ EXCLUDE functionality: STILL HAS ISSUES")
        
        return test_passed
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_fixed_dictionary_exclude()
    sys.exit(0 if success else 1)
