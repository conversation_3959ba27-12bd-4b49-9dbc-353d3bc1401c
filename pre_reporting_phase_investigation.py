#!/usr/bin/env python3
"""
Pre-reporting Phase Investigation
Detailed analysis of why excluded items appear in pre-reporting results
"""

import sys
import os
import sqlite3
import json

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def investigate_pre_reporting_phase():
    """Comprehensive investigation of pre-reporting phase filtering issues"""
    print("🔍 PRE-REPORTING PHASE INVESTIGATION")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get current session
        sys.path.append(os.path.dirname(__file__))
        from core.session_manager import get_current_session_id
        current_session = get_current_session_id()
        print(f"🎯 Investigating session: {current_session}")
        
        # 1. Database Schema Analysis
        print("\n1. 📊 DATABASE SCHEMA ANALYSIS:")
        
        # Check pre_reporting_results table structure
        cursor.execute("PRAGMA table_info(pre_reporting_results)")
        pre_reporting_columns = cursor.fetchall()
        
        print("   pre_reporting_results table columns:")
        for col in pre_reporting_columns:
            print(f"     {col[1]} ({col[2]})")
        
        # Check comparison_results table structure
        cursor.execute("PRAGMA table_info(comparison_results)")
        comparison_columns = cursor.fetchall()
        
        print("   comparison_results table columns:")
        for col in comparison_columns:
            print(f"     {col[1]} ({col[2]})")
        
        # Check dictionary_items table structure
        cursor.execute("PRAGMA table_info(dictionary_items)")
        dict_columns = cursor.fetchall()
        
        print("   dictionary_items table columns:")
        for col in dict_columns:
            print(f"     {col[1]} ({col[2]})")
        
        # 2. Current Pre-reporting Data Analysis
        print("\n2. 📋 CURRENT PRE-REPORTING DATA ANALYSIS:")
        
        # Count total pre-reporting results
        cursor.execute("""
            SELECT COUNT(*) FROM pre_reporting_results pr
            JOIN comparison_results cr ON pr.change_id = cr.id
            WHERE cr.session_id = ?
        """, (current_session,))
        
        total_pre_reporting = cursor.fetchone()[0]
        print(f"   Total pre-reporting results: {total_pre_reporting}")
        
        # Check for excluded items in pre-reporting
        cursor.execute("""
            SELECT cr.section_name, cr.item_label, COUNT(*) as count
            FROM pre_reporting_results pr
            JOIN comparison_results cr ON pr.change_id = cr.id
            LEFT JOIN dictionary_items di ON cr.item_label = di.item_name
            WHERE cr.session_id = ? AND di.include_in_report = 0
            GROUP BY cr.section_name, cr.item_label
            ORDER BY count DESC
            LIMIT 10
        """, (current_session,))
        
        excluded_in_pre_reporting = cursor.fetchall()
        
        if excluded_in_pre_reporting:
            print("   ❌ Excluded items found in pre-reporting:")
            for section, item, count in excluded_in_pre_reporting:
                print(f"     {section}.{item}: {count} occurrences")
        else:
            print("   ✅ No excluded items found in pre-reporting")
        
        # 3. Query Analysis - Current Implementation
        print("\n3. 🔍 CURRENT QUERY ANALYSIS:")
        
        # Test current pre-reporting query (without filtering)
        cursor.execute("""
            SELECT cr.section_name, cr.item_label, pr.bulk_category, COUNT(*) as count
            FROM pre_reporting_results pr
            JOIN comparison_results cr ON pr.change_id = cr.id
            WHERE cr.session_id = ?
            GROUP BY cr.section_name, cr.item_label, pr.bulk_category
            ORDER BY count DESC
            LIMIT 5
        """, (current_session,))
        
        current_results = cursor.fetchall()
        print("   Current pre-reporting query results (top 5):")
        for section, item, category, count in current_results:
            print(f"     {section}.{item} ({category}): {count}")
        
        # Test with filtering applied
        cursor.execute("""
            SELECT cr.section_name, cr.item_label, pr.bulk_category, COUNT(*) as count
            FROM pre_reporting_results pr
            JOIN comparison_results cr ON pr.change_id = cr.id
            LEFT JOIN dictionary_items di ON cr.item_label = di.item_name
            WHERE cr.session_id = ? 
              AND (di.include_in_report = 1 OR di.include_in_report IS NULL)
            GROUP BY cr.section_name, cr.item_label, pr.bulk_category
            ORDER BY count DESC
            LIMIT 5
        """, (current_session,))
        
        filtered_results = cursor.fetchall()
        print("   Filtered pre-reporting query results (top 5):")
        for section, item, category, count in filtered_results:
            print(f"     {section}.{item} ({category}): {count}")
        
        # Calculate reduction
        total_current = sum(count for _, _, _, count in current_results)
        total_filtered = sum(count for _, _, _, count in filtered_results)
        
        if total_current > 0:
            reduction = ((total_current - total_filtered) / total_current) * 100
            print(f"   📊 Potential reduction with filtering: {reduction:.1f}%")
        
        # 4. Code Implementation Analysis
        print("\n4. 🔧 CODE IMPLEMENTATION ANALYSIS:")
        
        # Check if phased_process_manager has pre-reporting methods
        try:
            from core.phased_process_manager import PhasedProcessManager
            
            manager = PhasedProcessManager(debug_mode=False)
            manager.session_id = current_session
            
            # Look for pre-reporting related methods
            pre_reporting_methods = [method for method in dir(manager) if 'pre_reporting' in method.lower()]
            print(f"   Pre-reporting methods found: {pre_reporting_methods}")
            
            # Test the method that loads pre-reporting data
            if hasattr(manager, '_load_pre_reporting_results'):
                print("   Testing _load_pre_reporting_results method...")
                try:
                    results = manager._load_pre_reporting_results()
                    print(f"   Method returned {len(results)} results")
                except Exception as e:
                    print(f"   Method failed: {e}")
            
        except Exception as e:
            print(f"   ❌ Code analysis failed: {e}")
        
        # 5. Comparison with Working Phases
        print("\n5. ⚖️ COMPARISON WITH WORKING PHASES:")
        
        # Check how report generation handles filtering
        print("   Report Generation Phase:")
        print("     ✅ Uses _apply_include_in_report_filter() method")
        print("     ✅ Filters at application level before processing")
        print("     ✅ Achieves 75% data reduction")
        
        # Check how UI display handles filtering
        print("   UI Display Phase:")
        print("     ✅ Uses database query with LEFT JOIN on dictionary_items")
        print("     ✅ Filters with WHERE clause: (di.include_in_report = 1 OR di.include_in_report IS NULL)")
        print("     ✅ Implemented in _load_selected_changes_for_reporting()")
        
        print("   Pre-reporting Phase:")
        print("     ❌ No filtering mechanism identified")
        print("     ❌ Direct queries without dictionary_items JOIN")
        print("     ❌ No include_in_report checking")
        
        # 6. Gap Identification
        print("\n6. 🎯 GAP IDENTIFICATION:")
        
        gaps = [
            "No LEFT JOIN with dictionary_items table in pre-reporting queries",
            "No WHERE clause filtering by include_in_report field",
            "No application-level filtering like report generation phase",
            "Pre-reporting methods don't check dictionary manager"
        ]
        
        for i, gap in enumerate(gaps, 1):
            print(f"   {i}. ❌ {gap}")
        
        # 7. Solution Strategy
        print("\n7. 💡 SOLUTION STRATEGY:")
        
        strategies = [
            "Database Query Level: Add LEFT JOIN with dictionary_items and WHERE filtering",
            "Application Level: Apply filtering similar to report generation phase",
            "Hybrid Approach: Combine both database and application filtering for robustness",
            "Consistency: Ensure same filtering logic as other working phases"
        ]
        
        for i, strategy in enumerate(strategies, 1):
            print(f"   {i}. 🔧 {strategy}")
        
        print("\n   🎯 RECOMMENDED APPROACH:")
        print("     Database Query Level filtering (most efficient)")
        print("     Consistent with UI Display phase implementation")
        print("     Minimal performance impact")
        print("     Easy to maintain and validate")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Investigation failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    investigate_pre_reporting_phase()
