<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auto-Learning Frontend Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stats {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #007bff;
            flex: 1;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        .confidence-high { background-color: #d4edda; }
        .confidence-medium { background-color: #fff3cd; }
        .confidence-low { background-color: #f8d7da; }
        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin-right: 5px;
        }
        .btn-success { background-color: #28a745; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-primary { background-color: #007bff; color: white; }
        .test-controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #e9ecef;
            border-radius: 6px;
        }
        .log {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 20px;
        }
        .error { color: #dc3545; }
        .success { color: #28a745; }
        .info { color: #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Auto-Learning Frontend Test</h1>
        
        <div class="test-controls">
            <button class="btn btn-primary" onclick="testAPICall()">🔄 Test API Call</button>
            <button class="btn btn-primary" onclick="testDataMapping()">🔄 Test Data Mapping</button>
            <button class="btn btn-primary" onclick="simulateRefresh()">🔄 Simulate Refresh</button>
            <button class="btn btn-danger" onclick="clearLog()">🗑️ Clear Log</button>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="pending-count">0</div>
                <div class="stat-label">PENDING ITEMS</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="auto-added-count">0</div>
                <div class="stat-label">AUTO-ADDED TODAY</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="accuracy">100%</div>
                <div class="stat-label">LEARNING ACCURACY</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="status">Ready</div>
                <div class="stat-label">STATUS</div>
            </div>
        </div>
        
        <h3>Pending Items for Approval</h3>
        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Item Name</th>
                    <th>Suggested Section</th>
                    <th>Confidence</th>
                    <th>Source</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody id="pending-items-list">
                <tr>
                    <td colspan="6" style="text-align: center; color: #666;">Click "Test API Call" to load pending items</td>
                </tr>
            </tbody>
        </table>
        
        <div class="log" id="test-log">
            <div class="info">🔍 Auto-Learning Frontend Test Ready</div>
            <div class="info">📋 This test simulates the exact frontend data flow</div>
            <div class="info">🎯 Click "Test API Call" to verify the 163 pending items</div>
        </div>
    </div>

    <script>
        let pendingItems = [];
        
        function log(message, type = 'info') {
            const logElement = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            logElement.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
            log('🗑️ Log cleared', 'info');
        }
        
        async function testAPICall() {
            log('🔄 Testing API call...', 'info');
            
            try {
                // Simulate the API call using fetch to the Python script
                const response = await fetch('/api/get-pending-items');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                log(`📊 API Response: ${JSON.stringify(result).substring(0, 100)}...`, 'success');
                
                processAPIResponse(result);
                
            } catch (error) {
                log(`❌ API call failed: ${error.message}`, 'error');
                log('💡 Using simulated data for testing...', 'info');
                
                // Use simulated data that matches the real API response
                const simulatedResult = {
                    success: true,
                    count: 163,
                    data: generateSimulatedData()
                };
                
                processAPIResponse(simulatedResult);
            }
        }
        
        function generateSimulatedData() {
            const sections = ['DEDUCTIONS', 'EARNINGS', 'EMPLOYEE BANK DETAILS', 'EMPLOYERS CONTRIBUTION', 'PERSONAL DETAILS'];
            const items = [
                'SSF EMPLOYEE', 'TITHES', 'NET PAY', 'ACCOUNT NO.', 'BANK', 'BRANCH',
                'SAVING SCHEME (EMPLOYER)', 'SSF EMPLOYER', 'EMPLOYEE NAME', 'EMPLOYEE NO.'
            ];
            
            const data = [];
            for (let i = 0; i < 163; i++) {
                data.push({
                    id: 8197 + i,
                    session_id: 'audit_session_1751020498_27976e2a',
                    section_name: sections[i % sections.length],
                    item_label: items[i % items.length] + (i > 9 ? ` ${Math.floor(i/10)}` : ''),
                    confidence_score: 0.7 + (Math.random() * 0.3), // 0.7 to 1.0
                    auto_approved: false,
                    dictionary_updated: false,
                    created_at: '2025-06-27 10:47:14'
                });
            }
            return data;
        }
        
        function processAPIResponse(result) {
            log(`📊 Processing API response...`, 'info');
            
            // Handle both direct array and object with data property
            if (Array.isArray(result)) {
                pendingItems = result;
            } else {
                pendingItems = result.data || result.pending_items || [];
            }
            
            log(`📊 Extracted ${pendingItems.length} raw items`, 'info');
            
            // Apply the same data mapping as the frontend
            pendingItems = pendingItems.map((item, index) => {
                if (!item || typeof item !== 'object') {
                    log(`⚠️ Invalid item at index ${index}: ${item}`, 'error');
                    return null;
                }
                
                return {
                    id: item.id || item.discovery_id || `item_${index}_${Date.now()}`,
                    item_name: item.item_label || item.item_name || item.label || 'Unknown',
                    section: item.section_name || item.section || item.suggested_section || 'EARNINGS',
                    value: item.value || 'N/A',
                    confidence: typeof item.confidence_score === 'number' ? item.confidence_score :
                               (typeof item.confidence === 'number' ? item.confidence : 0),
                    source: item.source || 'Auto-detected',
                    auto_approved: item.auto_approved || false,
                    is_new_item: item.is_new_item !== false,
                    status: item.status || 'pending_approval',
                    timestamp: item.created_at || item.timestamp || new Date().toISOString()
                };
            }).filter(item => item !== null);
            
            log(`📊 Mapped ${pendingItems.length} items successfully`, 'success');
            
            updateStats();
            renderPendingItems();
        }
        
        function testDataMapping() {
            log('🔄 Testing data mapping with sample data...', 'info');
            
            const sampleData = {
                success: true,
                count: 3,
                data: [
                    {
                        id: 8197,
                        session_id: 'test_session',
                        section_name: 'DEDUCTIONS',
                        item_label: 'SSF EMPLOYEE',
                        confidence_score: 0.95,
                        auto_approved: false,
                        dictionary_updated: false,
                        created_at: '2025-06-27 10:47:14'
                    },
                    {
                        id: 8198,
                        session_id: 'test_session',
                        section_name: 'EARNINGS',
                        item_label: 'NET PAY',
                        confidence_score: 0.88,
                        auto_approved: false,
                        dictionary_updated: false,
                        created_at: '2025-06-27 10:47:15'
                    },
                    {
                        id: 8199,
                        session_id: 'test_session',
                        section_name: 'EMPLOYEE BANK DETAILS',
                        item_label: 'ACCOUNT NO.',
                        confidence_score: 0.72,
                        auto_approved: false,
                        dictionary_updated: false,
                        created_at: '2025-06-27 10:47:16'
                    }
                ]
            };
            
            processAPIResponse(sampleData);
        }
        
        function simulateRefresh() {
            log('🔄 Simulating refresh with full dataset...', 'info');
            testAPICall();
        }
        
        function updateStats() {
            const stats = {
                pending_count: pendingItems.length,
                auto_added_today: pendingItems.filter(item => item.auto_approved).length,
                learning_accuracy: '100%',
                status: 'Ready'
            };
            
            document.getElementById('pending-count').textContent = stats.pending_count;
            document.getElementById('auto-added-count').textContent = stats.auto_added_today;
            document.getElementById('accuracy').textContent = stats.learning_accuracy;
            document.getElementById('status').textContent = stats.status;
            
            log(`📊 Stats updated: ${JSON.stringify(stats)}`, 'success');
        }
        
        function renderPendingItems() {
            const tbody = document.getElementById('pending-items-list');
            tbody.innerHTML = '';
            
            if (pendingItems.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; color: #666;">No pending items for approval</td></tr>';
                return;
            }
            
            pendingItems.slice(0, 20).forEach((item, index) => { // Show first 20 items
                const row = document.createElement('tr');
                
                const confidenceClass = item.confidence > 0.8 ? 'confidence-high' : 
                                       (item.confidence > 0.5 ? 'confidence-medium' : 'confidence-low');
                
                row.innerHTML = `
                    <td>${item.id}</td>
                    <td>${item.item_name}</td>
                    <td>${item.section}</td>
                    <td class="${confidenceClass}">${Math.round(item.confidence * 100)}%</td>
                    <td>${item.source}</td>
                    <td>
                        <button class="btn btn-success" onclick="approveItem(${item.id})">✓ Approve</button>
                        <button class="btn btn-danger" onclick="rejectItem(${item.id})">✗ Reject</button>
                    </td>
                `;
                
                tbody.appendChild(row);
            });
            
            if (pendingItems.length > 20) {
                const moreRow = document.createElement('tr');
                moreRow.innerHTML = `<td colspan="6" style="text-align: center; color: #666; font-style: italic;">... and ${pendingItems.length - 20} more items</td>`;
                tbody.appendChild(moreRow);
            }
            
            log(`🎨 Rendered ${Math.min(pendingItems.length, 20)} items in table`, 'success');
        }
        
        function approveItem(itemId) {
            log(`✅ Approve item ${itemId} (simulated)`, 'success');
        }
        
        function rejectItem(itemId) {
            log(`❌ Reject item ${itemId} (simulated)`, 'error');
        }
        
        // Auto-run test on page load
        window.addEventListener('load', () => {
            log('🚀 Page loaded, running automatic test...', 'info');
            setTimeout(testAPICall, 1000);
        });
    </script>
</body>
</html>
