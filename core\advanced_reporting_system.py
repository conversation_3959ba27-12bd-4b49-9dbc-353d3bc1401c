#!/usr/bin/env python3
"""
ADVANCED REPORTING SYSTEM
Custom templates and advanced reporting for Bank Adviser
Supports multiple output formats and custom Church of Pentecost templates
"""

import os
import sys
import json
from datetime import datetime
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import tempfile

try:
    from openpyxl import Workbook, load_workbook
    from openpyxl.styles import Font, Alignment, Border, Side, PatternFill, NamedStyle
    from openpyxl.utils import get_column_letter
    from openpyxl.chart import BarChart, Reference
    from openpyxl.drawing.image import Image
except ImportError:
    print("[WARNING] openpyxl not available. Excel features will be limited.")

@dataclass
class ReportTemplate:
    """Report template configuration"""
    name: str
    description: str
    template_type: str  # 'bank_advice', 'summary', 'analysis'
    church_branding: bool = True
    include_charts: bool = False
    include_summary: bool = True
    custom_fields: List[str] = None
    output_format: str = 'xlsx'  # 'xlsx', 'pdf', 'csv'

class AdvancedReportingSystem:
    """
    Advanced reporting system with custom templates and multiple output formats
    """
    
    def __init__(self):
        self.church_name = "THE CHURCH OF PENTECOST"
        self.templates = self._load_default_templates()
        self.custom_styles = self._create_custom_styles()
        
        print("[ADVANCED REPORTING] System initialized with custom templates")

    def _load_default_templates(self) -> Dict[str, ReportTemplate]:
        """Load default report templates"""
        return {
            'standard_bank_advice': ReportTemplate(
                name="Standard Bank Advice",
                description="Standard Church of Pentecost bank advice format",
                template_type="bank_advice",
                church_branding=True,
                include_charts=False,
                include_summary=True,
                custom_fields=['employee_no', 'employee_name', 'department', 'account_no', 'amount']
            ),
            'detailed_bank_advice': ReportTemplate(
                name="Detailed Bank Advice",
                description="Detailed bank advice with tax breakdown",
                template_type="bank_advice",
                church_branding=True,
                include_charts=False,
                include_summary=True,
                custom_fields=['employee_no', 'employee_name', 'department', 'section', 
                             'account_no', 'gross_amount', 'tax_amount', 'payable_amount']
            ),
            'executive_summary': ReportTemplate(
                name="Executive Summary",
                description="High-level summary for church leadership",
                template_type="summary",
                church_branding=True,
                include_charts=True,
                include_summary=True,
                custom_fields=['total_employees', 'total_banks', 'total_amount', 'by_department']
            ),
            'financial_analysis': ReportTemplate(
                name="Financial Analysis",
                description="Detailed financial analysis with trends",
                template_type="analysis",
                church_branding=True,
                include_charts=True,
                include_summary=True,
                custom_fields=['monthly_trends', 'department_breakdown', 'bank_distribution']
            ),
            'audit_report': ReportTemplate(
                name="Audit Report",
                description="Comprehensive audit report for compliance",
                template_type="analysis",
                church_branding=True,
                include_charts=False,
                include_summary=True,
                custom_fields=['validation_results', 'discrepancies', 'recommendations']
            )
        }

    def _create_custom_styles(self) -> Dict[str, NamedStyle]:
        """Create custom Excel styles for Church of Pentecost branding"""
        styles = {}
        
        # Church Header Style
        church_header = NamedStyle(name="church_header")
        church_header.font = Font(name='Arial', size=16, bold=True, color='FFFFFF')
        church_header.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        church_header.alignment = Alignment(horizontal='center', vertical='center')
        church_header.border = Border(
            left=Side(style='thin'), right=Side(style='thin'),
            top=Side(style='thin'), bottom=Side(style='thin')
        )
        styles['church_header'] = church_header
        
        # Section Header Style
        section_header = NamedStyle(name="section_header")
        section_header.font = Font(name='Arial', size=12, bold=True, color='000000')
        section_header.fill = PatternFill(start_color="D5E8D4", end_color="D5E8D4", fill_type="solid")
        section_header.alignment = Alignment(horizontal='center', vertical='center')
        section_header.border = Border(
            left=Side(style='thin'), right=Side(style='thin'),
            top=Side(style='thin'), bottom=Side(style='thin')
        )
        styles['section_header'] = section_header
        
        # Data Cell Style
        data_cell = NamedStyle(name="data_cell")
        data_cell.font = Font(name='Arial', size=10)
        data_cell.alignment = Alignment(horizontal='left', vertical='center')
        data_cell.border = Border(
            left=Side(style='thin'), right=Side(style='thin'),
            top=Side(style='thin'), bottom=Side(style='thin')
        )
        styles['data_cell'] = data_cell
        
        # Currency Cell Style
        currency_cell = NamedStyle(name="currency_cell")
        currency_cell.font = Font(name='Arial', size=10)
        currency_cell.alignment = Alignment(horizontal='right', vertical='center')
        currency_cell.number_format = '#,##0.00'
        currency_cell.border = Border(
            left=Side(style='thin'), right=Side(style='thin'),
            top=Side(style='thin'), bottom=Side(style='thin')
        )
        styles['currency_cell'] = currency_cell
        
        # Total Row Style
        total_row = NamedStyle(name="total_row")
        total_row.font = Font(name='Arial', size=11, bold=True)
        total_row.fill = PatternFill(start_color="F2F2F2", end_color="F2F2F2", fill_type="solid")
        total_row.alignment = Alignment(horizontal='right', vertical='center')
        total_row.number_format = '#,##0.00'
        total_row.border = Border(
            left=Side(style='medium'), right=Side(style='medium'),
            top=Side(style='medium'), bottom=Side(style='medium')
        )
        styles['total_row'] = total_row
        
        return styles

    def _apply_include_in_report_filter(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        PRODUCTION SAFEGUARD: Filter data based on include_in_report settings

        Args:
            data: Raw report data

        Returns:
            Filtered data with only items that should be included in reports
        """
        try:
            # PRODUCTION FIX: Handle None and invalid data types
            if data is None:
                return {}

            if not isinstance(data, dict):
                print(f"[REPORT FILTER] Warning: Invalid data type {type(data)}, returning empty dict")
                return {}

            from core.dictionary_manager import PayrollDictionaryManager

            # Initialize dictionary manager for filtering
            dict_manager = PayrollDictionaryManager(debug=False)

            filtered_data = {}

            # Filter each section of the data
            for key, value in data.items():
                if isinstance(value, list):
                    # Filter list of items (e.g., comparison results, extracted data)
                    filtered_items = []
                    for item in value:
                        if isinstance(item, dict):
                            section_name = item.get('section_name', item.get('section', ''))
                            item_name = item.get('item_label', item.get('item_name', item.get('label', '')))

                            # Check if item should be included in report
                            if section_name and item_name:
                                if dict_manager.should_include_in_report(section_name, item_name):
                                    filtered_items.append(item)
                                else:
                                    print(f"[REPORT FILTER] Excluding {section_name}.{item_name} from report")
                            else:
                                # Include items without section/name info (metadata, etc.)
                                filtered_items.append(item)
                        else:
                            # Include non-dict items
                            filtered_items.append(item)

                    filtered_data[key] = filtered_items

                elif isinstance(value, dict):
                    # Recursively filter nested dictionaries
                    filtered_data[key] = self._apply_include_in_report_filter(value)
                else:
                    # Include primitive values as-is
                    filtered_data[key] = value

            print(f"[REPORT FILTER] Applied include_in_report filtering to {len(data)} data sections")
            return filtered_data

        except Exception as e:
            print(f"[REPORT FILTER] Warning: Filtering failed, using original data: {e}")
            return data  # Fallback to original data if filtering fails

    def generate_custom_report(self, data: Dict[str, Any], template_name: str,
                             output_path: str, custom_options: Dict = None) -> Dict[str, Any]:
        """Generate custom report using specified template"""

        if template_name not in self.templates:
            return {
                'success': False,
                'error': f'Template "{template_name}" not found'
            }

        template = self.templates[template_name]

        print(f"[ADVANCED REPORTING] Generating {template.name} report")

        # PRODUCTION SAFEGUARD: Apply include_in_report filtering before report generation
        filtered_data = self._apply_include_in_report_filter(data)

        try:
            if template.output_format == 'xlsx':
                result = self._generate_excel_report(filtered_data, template, output_path, custom_options)
            elif template.output_format == 'pdf':
                result = self._generate_pdf_report(filtered_data, template, output_path, custom_options)
            elif template.output_format == 'csv':
                result = self._generate_csv_report(filtered_data, template, output_path, custom_options)
            else:
                return {
                    'success': False,
                    'error': f'Unsupported output format: {template.output_format}'
                }

            return result

        except Exception as e:
            return {
                'success': False,
                'error': f'Report generation failed: {str(e)}'
            }

    def _generate_excel_report(self, data: Dict[str, Any], template: ReportTemplate, 
                             output_path: str, custom_options: Dict = None) -> Dict[str, Any]:
        """Generate Excel report with custom template"""
        
        wb = Workbook()
        
        # Add custom styles to workbook
        for style_name, style in self.custom_styles.items():
            wb.add_named_style(style)
        
        # Remove default sheet
        wb.remove(wb.active)
        
        if template.template_type == 'bank_advice':
            result = self._create_bank_advice_sheets(wb, data, template, custom_options)
        elif template.template_type == 'summary':
            result = self._create_summary_sheet(wb, data, template, custom_options)
        elif template.template_type == 'analysis':
            result = self._create_analysis_sheets(wb, data, template, custom_options)
        
        # Save workbook
        wb.save(output_path)
        
        return {
            'success': True,
            'template_used': template.name,
            'output_path': output_path,
            'sheets_created': len(wb.worksheets),
            'report_type': template.template_type
        }

    def _create_bank_advice_sheets(self, wb: Workbook, data: Dict[str, Any], 
                                 template: ReportTemplate, custom_options: Dict = None) -> Dict:
        """Create bank advice sheets with custom template"""
        
        bank_groups = data.get('banks', {})
        sheets_created = 0
        
        for bank_name, bank_data in bank_groups.items():
            # Create sheet for each bank
            ws = wb.create_sheet(title=self._sanitize_sheet_name(bank_name))
            
            # Add Church of Pentecost header
            if template.church_branding:
                self._add_church_header(ws, template, bank_name)
            
            # Add bank advice table
            self._add_bank_advice_table(ws, bank_data, template)
            
            # Add summary if requested
            if template.include_summary:
                self._add_bank_summary(ws, bank_data, template)
            
            # Add charts if requested
            if template.include_charts:
                self._add_bank_charts(ws, bank_data, template)
            
            sheets_created += 1
        
        return {'sheets_created': sheets_created}

    def _create_summary_sheet(self, wb: Workbook, data: Dict[str, Any], 
                            template: ReportTemplate, custom_options: Dict = None) -> Dict:
        """Create executive summary sheet"""
        
        ws = wb.create_sheet(title="Executive Summary")
        
        # Church header
        if template.church_branding:
            self._add_church_header(ws, template, "Executive Summary")
        
        # Summary statistics
        self._add_executive_summary_table(ws, data, template)
        
        # Charts
        if template.include_charts:
            self._add_summary_charts(ws, data, template)
        
        return {'summary_created': True}

    def _create_analysis_sheets(self, wb: Workbook, data: Dict[str, Any], 
                              template: ReportTemplate, custom_options: Dict = None) -> Dict:
        """Create detailed analysis sheets"""
        
        # Financial Analysis Sheet
        ws_financial = wb.create_sheet(title="Financial Analysis")
        if template.church_branding:
            self._add_church_header(ws_financial, template, "Financial Analysis")
        self._add_financial_analysis(ws_financial, data, template)
        
        # Department Analysis Sheet
        ws_dept = wb.create_sheet(title="Department Analysis")
        if template.church_branding:
            self._add_church_header(ws_dept, template, "Department Analysis")
        self._add_department_analysis(ws_dept, data, template)
        
        # Validation Report Sheet
        ws_validation = wb.create_sheet(title="Validation Report")
        if template.church_branding:
            self._add_church_header(ws_validation, template, "Validation Report")
        self._add_validation_report(ws_validation, data, template)
        
        return {'analysis_sheets_created': 3}

    def _add_church_header(self, ws, template: ReportTemplate, title: str):
        """Add Church of Pentecost header to worksheet"""
        
        # Church name
        ws['A1'] = self.church_name
        ws['A1'].style = 'church_header'
        ws.merge_cells('A1:G1')
        
        # Report title
        ws['A3'] = title.upper()
        ws['A3'].style = 'section_header'
        ws.merge_cells('A3:G3')
        
        # Date and time
        ws['A4'] = f"Generated: {datetime.now().strftime('%d/%m/%Y %H:%M')}"
        ws['A4'].style = 'data_cell'
        
        # Template info
        ws['E4'] = f"Template: {template.name}"
        ws['E4'].style = 'data_cell'

    def _add_bank_advice_table(self, ws, bank_data: Dict, template: ReportTemplate):
        """Add bank advice table with custom fields"""
        
        start_row = 6
        records = bank_data.get('records', [])
        
        # Headers based on template custom fields
        headers = []
        if 'employee_no' in template.custom_fields:
            headers.append('Employee No.')
        if 'employee_name' in template.custom_fields:
            headers.append('Employee Name')
        if 'department' in template.custom_fields:
            headers.append('Department')
        if 'section' in template.custom_fields:
            headers.append('Section')
        if 'account_no' in template.custom_fields:
            headers.append('Account No.')
        if 'gross_amount' in template.custom_fields:
            headers.append('Gross (GHS)')
        if 'tax_amount' in template.custom_fields:
            headers.append('Tax (GHS)')
        if 'payable_amount' in template.custom_fields or 'amount' in template.custom_fields:
            headers.append('Payable (GHS)')
        
        # Add headers
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=start_row, column=col, value=header)
            cell.style = 'section_header'
        
        # Add data
        current_row = start_row + 1
        total_amount = 0.0
        
        for i, record in enumerate(records, 1):
            col = 1
            
            if 'employee_no' in template.custom_fields:
                ws.cell(row=current_row, column=col, value=record.get('employee_no', '')).style = 'data_cell'
                col += 1
            if 'employee_name' in template.custom_fields:
                ws.cell(row=current_row, column=col, value=record.get('employee_name', '')).style = 'data_cell'
                col += 1
            if 'department' in template.custom_fields:
                ws.cell(row=current_row, column=col, value=record.get('department', '')).style = 'data_cell'
                col += 1
            if 'section' in template.custom_fields:
                ws.cell(row=current_row, column=col, value=record.get('section', '')).style = 'data_cell'
                col += 1
            if 'account_no' in template.custom_fields:
                ws.cell(row=current_row, column=col, value=record.get('account_no', '')).style = 'data_cell'
                col += 1
            if 'gross_amount' in template.custom_fields:
                gross = record.get('gross_amount', 0)
                ws.cell(row=current_row, column=col, value=gross).style = 'currency_cell'
                col += 1
            if 'tax_amount' in template.custom_fields:
                tax = record.get('tax_amount', 0)
                ws.cell(row=current_row, column=col, value=tax).style = 'currency_cell'
                col += 1
            if 'payable_amount' in template.custom_fields or 'amount' in template.custom_fields:
                amount = record.get('payable_amount', record.get('net_pay', 0))
                ws.cell(row=current_row, column=col, value=amount).style = 'currency_cell'
                total_amount += amount
            
            current_row += 1
        
        # Total row
        total_col = len(headers)
        ws.cell(row=current_row, column=total_col - 1, value="TOTAL:").style = 'total_row'
        ws.cell(row=current_row, column=total_col, value=total_amount).style = 'total_row'
        
        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width

    def _add_bank_summary(self, ws, bank_data: Dict, template: ReportTemplate):
        """Add bank summary section"""
        
        # Find the last used row
        last_row = ws.max_row + 3
        
        # Summary header
        ws.cell(row=last_row, column=1, value="BANK SUMMARY").style = 'section_header'
        ws.merge_cells(f'A{last_row}:D{last_row}')
        
        # Summary data
        records = bank_data.get('records', [])
        total_amount = sum(r.get('payable_amount', r.get('net_pay', 0)) for r in records)
        unique_employees = len(set(r.get('employee_no') for r in records))
        
        summary_data = [
            ('Total Records:', len(records)),
            ('Unique Employees:', unique_employees),
            ('Total Amount:', f'GHS {total_amount:,.2f}'),
            ('Bank Name:', bank_data.get('bank_name', 'Unknown'))
        ]
        
        for i, (label, value) in enumerate(summary_data):
            row = last_row + 2 + i
            ws.cell(row=row, column=1, value=label).style = 'data_cell'
            ws.cell(row=row, column=2, value=value).style = 'data_cell'

    def _add_bank_charts(self, ws, bank_data: Dict, template: ReportTemplate):
        """Add charts to bank advice sheet"""
        # Chart implementation would go here
        # For now, just add a placeholder
        pass

    def _add_executive_summary_table(self, ws, data: Dict, template: ReportTemplate):
        """Add executive summary table"""
        # Implementation for executive summary
        pass

    def _add_summary_charts(self, ws, data: Dict, template: ReportTemplate):
        """Add summary charts"""
        # Implementation for summary charts
        pass

    def _add_financial_analysis(self, ws, data: Dict, template: ReportTemplate):
        """Add financial analysis"""
        # Implementation for financial analysis
        pass

    def _add_department_analysis(self, ws, data: Dict, template: ReportTemplate):
        """Add department analysis"""
        # Implementation for department analysis
        pass

    def _add_validation_report(self, ws, data: Dict, template: ReportTemplate):
        """Add validation report"""
        # Implementation for validation report
        pass

    def _generate_pdf_report(self, data: Dict, template: ReportTemplate, 
                           output_path: str, custom_options: Dict = None) -> Dict:
        """Generate PDF report (placeholder)"""
        return {
            'success': False,
            'error': 'PDF generation not yet implemented'
        }

    def _generate_csv_report(self, data: Dict, template: ReportTemplate, 
                           output_path: str, custom_options: Dict = None) -> Dict:
        """Generate CSV report (placeholder)"""
        return {
            'success': False,
            'error': 'CSV generation not yet implemented'
        }

    def _sanitize_sheet_name(self, name: str) -> str:
        """Sanitize sheet name for Excel compatibility"""
        invalid_chars = ['\\', '/', '*', '[', ']', ':', '?']
        for char in invalid_chars:
            name = name.replace(char, '_')
        return name[:31]

    def list_available_templates(self) -> List[Dict[str, str]]:
        """List all available report templates"""
        return [
            {
                'name': template.name,
                'description': template.description,
                'type': template.template_type,
                'key': key
            }
            for key, template in self.templates.items()
        ]

def main():
    """Command line interface for Advanced Reporting System"""
    
    reporting_system = AdvancedReportingSystem()
    
    # List available templates
    print("[ADVANCED REPORTING] Available Templates:")
    for template in reporting_system.list_available_templates():
        print(f"  - {template['name']}: {template['description']}")

if __name__ == "__main__":
    main()
