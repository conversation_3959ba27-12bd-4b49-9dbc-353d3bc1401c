
// PRODUCTION FIX: Enhanced error handling for unified_database.js saveDictionary method
// Add this error handling wrapper around the saveDictionary method:

async saveDictionary(dictionary) {
    try {
        console.log('[UNIFIED_DB] Starting dictionary save operation...');
        
        // Validate input
        if (!dictionary || typeof dictionary !== 'object') {
            throw new Error('Invalid dictionary data provided');
        }
        
        const queries = [];
        
        // Clear existing data with error handling
        try {
            queries.push({ sql: 'DELETE FROM dictionary_items', params: [] });
            queries.push({ sql: 'DELETE FROM dictionary_sections', params: [] });
        } catch (clearError) {
            console.error('[UNIFIED_DB] Error preparing clear queries:', clearError);
            throw new Error(`Failed to prepare clear operations: ${clearError.message}`);
        }
        
        // Process sections with detailed error reporting
        for (const [sectionName, sectionData] of Object.entries(dictionary)) {
            try {
                queries.push({
                    sql: 'INSERT INTO dictionary_sections (section_name) VALUES (?)',
                    params: [sectionName]
                });
                
                if (sectionData.items) {
                    for (const [itemName, itemData] of Object.entries(sectionData.items)) {
                        try {
                            queries.push({
                                sql: `INSERT OR REPLACE INTO dictionary_items
                                      (section_id, item_name, standard_key, format_type, value_format,
                                       include_in_report, include_new, include_increase, include_decrease, 
                                       include_removed, include_no_change, is_fixed, validation_rules)
                                      VALUES ((SELECT id FROM dictionary_sections WHERE section_name = ?),
                                              ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                                params: [
                                    sectionName, itemName, itemData.standardized_name || itemName,
                                    itemData.format || 'text', itemData.value_format || 'text',
                                    itemData.include_in_report ? 1 : 0,
                                    itemData.include_new !== undefined ? (itemData.include_new ? 1 : 0) : 1,
                                    itemData.include_increase !== undefined ? (itemData.include_increase ? 1 : 0) : 1,
                                    itemData.include_decrease !== undefined ? (itemData.include_decrease ? 1 : 0) : 1,
                                    itemData.include_removed !== undefined ? (itemData.include_removed ? 1 : 0) : 1,
                                    itemData.include_no_change !== undefined ? (itemData.include_no_change ? 1 : 0) : 0,
                                    itemData.is_fixed ? 1 : 0,
                                    JSON.stringify(itemData.validation_rules || {})
                                ]
                            });
                        } catch (itemError) {
                            console.error(`[UNIFIED_DB] Error processing item ${itemName} in section ${sectionName}:`, itemError);
                            throw new Error(`Failed to process item ${itemName}: ${itemError.message}`);
                        }
                    }
                }
            } catch (sectionError) {
                console.error(`[UNIFIED_DB] Error processing section ${sectionName}:`, sectionError);
                throw new Error(`Failed to process section ${sectionName}: ${sectionError.message}`);
            }
        }
        
        // Execute transaction with detailed error reporting
        try {
            const result = await this.executeTransaction(queries);
            console.log(`[UNIFIED_DB] ✅ Dictionary saved successfully: ${queries.length} operations`);
            return result;
        } catch (transactionError) {
            console.error('[UNIFIED_DB] Transaction failed:', transactionError);
            throw new Error(`Database transaction failed: ${transactionError.message}`);
        }
        
    } catch (error) {
        console.error('[UNIFIED_DB] ❌ Dictionary save operation failed:', error);
        throw error; // Re-throw to trigger fallback
    }
}
