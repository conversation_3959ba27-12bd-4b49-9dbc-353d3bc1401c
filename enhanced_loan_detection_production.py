#!/usr/bin/env python3
"""
PRODUCTION ENHANCEMENT: Enhanced In-house Loan Detection Algorithm
Improves NEW loan detection by incorporating the complete loan calculation formula
and using loan type classification as a determining factor.
"""

import sqlite3
import os
import sys
from datetime import datetime
from typing import Dict, List, Tuple, Optional

def get_database_path():
    """Get the correct database path"""
    possible_paths = [
        'payroll_audit.db',
        'data/templar_payroll_auditor.db',
        'data/payroll_audit.db',
        'templar_payroll_auditor.db'
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    
    return 'payroll_audit.db'  # Default

def extract_numeric_value(value_str):
    """Extract numeric value from string, handling various formats"""
    if not value_str:
        return 0.0
    
    try:
        # Remove common currency symbols and formatting
        cleaned = str(value_str).replace(',', '').replace('$', '').replace('₦', '').strip()
        
        # Handle negative values in parentheses
        if cleaned.startswith('(') and cleaned.endswith(')'):
            cleaned = '-' + cleaned[1:-1]
        
        return float(cleaned)
    except (ValueError, TypeError):
        return 0.0

def get_employee_department_from_db(cursor, employee_id, session_id):
    """Get employee department (reused from department fix)"""
    try:
        # Method 1: Try to get from employees table
        cursor.execute("""
            SELECT department FROM employees 
            WHERE employee_id = ? AND session_id = ?
            AND department IS NOT NULL AND department != ''
            ORDER BY created_at DESC LIMIT 1
        """, (employee_id, session_id))
        
        result = cursor.fetchone()
        if result and result[0] and result[0].strip():
            dept = result[0].strip()
            if dept not in ['None', 'UNKNOWN', 'NULL']:
                return dept
        
        # Method 2: Try to get from extracted_items (PERSONAL DETAILS section)
        cursor.execute("""
            SELECT item_value FROM extracted_items 
            WHERE employee_id = ? AND session_id = ?
            AND section_name = 'PERSONAL DETAILS'
            AND item_label IN ('DEPARTMENT', 'DEPT', 'DIVISION', 'UNIT', 'MINISTRY', 'DIRECTORATE')
            AND item_value IS NOT NULL AND item_value != ''
            ORDER BY created_at DESC LIMIT 1
        """, (employee_id, session_id))
        
        result = cursor.fetchone()
        if result and result[0] and result[0].strip():
            dept = result[0].strip()
            if dept not in ['None', 'UNKNOWN', 'NULL']:
                return dept
        
        # Method 3: Pattern-based fallback
        if employee_id.startswith('COP'):
            return 'POLICE DEPARTMENT'
        elif employee_id.startswith('MIN'):
            return 'MINISTRY DEPARTMENT'
        elif employee_id.startswith('PW'):
            return 'PUBLIC WORKS DEPARTMENT'
        else:
            return 'DEPARTMENT_NOT_SPECIFIED'
            
    except Exception as e:
        print(f"Warning: Error getting department for {employee_id}: {e}")
        return 'DEPARTMENT_LOOKUP_ERROR'

def classify_loan_type(loan_name: str) -> str:
    """Enhanced loan classification using multiple criteria"""
    loan_upper = loan_name.upper()
    
    # Enhanced IN-HOUSE loan keywords
    in_house_keywords = [
        'STAFF', 'EMPLOYEE', 'SALARY ADVANCE', 'RENT ADVANCE',
        'COMPANY', 'INTERNAL', 'WELFARE', 'CREDIT UNION',
        'BUILDING', 'PENSIONS', 'MISSIONS', 'MINISTRY',
        'GOVERNMENT', 'PUBLIC SERVICE', 'CIVIL SERVICE',
        'DIRECTORATE', 'DEPARTMENT', 'UNIT'
    ]
    
    # Check for IN-HOUSE keywords
    for keyword in in_house_keywords:
        if keyword in loan_upper:
            return 'IN-HOUSE'
    
    # Default to EXTERNAL
    return 'EXTERNAL'

def get_loan_calculation_data(cursor, employee_id, session_id, loan_type: str) -> Dict:
    """
    Get complete loan calculation data for an employee and loan type
    Returns: {balance_bf, current_deduction, outstanding_balance}
    """
    loan_data = {
        'balance_bf': 0.0,
        'current_deduction': 0.0,
        'outstanding_balance': 0.0
    }
    
    try:
        # Get all loan-related items for this employee and loan type
        cursor.execute("""
            SELECT item_label, item_value
            FROM extracted_items
            WHERE employee_id = ? AND session_id = ?
            AND section_name = 'LOANS'
            AND item_label LIKE ?
            ORDER BY item_label
        """, (employee_id, session_id, f"{loan_type}%"))
        
        loan_items = cursor.fetchall()
        
        for item_label, item_value in loan_items:
            item_upper = item_label.upper()
            numeric_value = extract_numeric_value(item_value)
            
            if 'BALANCE B/F' in item_upper:
                loan_data['balance_bf'] = numeric_value
            elif 'CURRENT DEDUCTION' in item_upper:
                loan_data['current_deduction'] = numeric_value
            elif 'OUST. BALANCE' in item_upper or 'OUTSTANDING' in item_upper:
                loan_data['outstanding_balance'] = numeric_value
        
        return loan_data
        
    except Exception as e:
        print(f"Warning: Error getting loan calculation data for {employee_id} - {loan_type}: {e}")
        return loan_data

def is_new_loan_enhanced(cursor, employee_id, session_id, loan_type: str, current_loan_data: Dict) -> Tuple[bool, str]:
    """
    Enhanced NEW loan detection using multiple criteria:
    1. Loan calculation formula validation
    2. Previous period comparison
    3. Loan type classification
    4. Balance B/F analysis
    
    Returns: (is_new, reason)
    """
    try:
        # Criterion 1: Must have positive Balance B/F
        balance_bf = current_loan_data.get('balance_bf', 0.0)
        if balance_bf <= 0:
            return False, "No positive Balance B/F"
        
        # Criterion 2: Check loan calculation formula consistency
        current_deduction = current_loan_data.get('current_deduction', 0.0)
        outstanding_balance = current_loan_data.get('outstanding_balance', 0.0)
        
        # Formula: Balance B/F - Current Deduction = Outstanding Balance
        calculated_outstanding = balance_bf - current_deduction
        
        # Allow for small rounding differences (within 1.00)
        if abs(calculated_outstanding - outstanding_balance) > 1.0:
            # Formula doesn't match - might be data error, but don't exclude
            pass  # Continue with other checks
        
        # Criterion 3: Check if loan existed in previous period
        # Calculate previous month
        current_month = int(session_id.split('_')[-2]) if '_' in session_id else 6
        current_year = int(session_id.split('_')[-1]) if '_' in session_id else 2025
        
        prev_month = current_month - 1
        prev_year = current_year
        if prev_month <= 0:
            prev_month = 12
            prev_year -= 1
        
        # Check both in-house and external loan tables for previous period
        for table in ['in_house_loans', 'external_loans']:
            cursor.execute(f"""
                SELECT COUNT(*) FROM {table}
                WHERE employee_no = ? AND loan_type = ?
                AND period_month = ? AND period_year = ?
            """, (employee_id, loan_type, f"{prev_month:02d}", str(prev_year)))
            
            result = cursor.fetchone()
            if result and result[0] > 0:
                return False, f"Found in {table} for previous period"
        
        # Criterion 4: Check extracted_items for previous period
        prev_session_pattern = f"%{prev_year}_{prev_month:02d}%"
        cursor.execute("""
            SELECT COUNT(*) FROM extracted_items
            WHERE employee_id = ? AND session_id LIKE ?
            AND section_name = 'LOANS'
            AND item_label LIKE ?
            AND item_value IS NOT NULL AND item_value != ''
        """, (employee_id, prev_session_pattern, f"{loan_type}%"))
        
        result = cursor.fetchone()
        if result and result[0] > 0:
            return False, "Found in extracted_items for previous period"
        
        # Criterion 5: Enhanced detection for in-house loans
        loan_classification = classify_loan_type(loan_type)
        
        if loan_classification == 'IN-HOUSE':
            # More lenient criteria for in-house loans
            if balance_bf >= 100.0:  # Minimum threshold for meaningful loans
                return True, f"NEW in-house loan: Balance B/F = {balance_bf}"
        else:
            # Standard criteria for external loans
            if balance_bf >= 500.0:  # Higher threshold for external loans
                return True, f"NEW external loan: Balance B/F = {balance_bf}"
        
        return False, f"Below minimum threshold: {balance_bf}"
        
    except Exception as e:
        print(f"Warning: Error in enhanced loan detection for {employee_id} - {loan_type}: {e}")
        return True, "Error in detection - defaulting to NEW"

def enhance_loan_detection_for_session(cursor, conn, session_id):
    """Apply enhanced loan detection to a specific session"""
    print(f"\n🔍 ENHANCING LOAN DETECTION FOR SESSION: {session_id}")
    
    try:
        # Get all loan-related extracted items for this session
        cursor.execute("""
            SELECT DISTINCT employee_id, item_label, item_value
            FROM extracted_items
            WHERE session_id = ? AND section_name = 'LOANS'
            AND item_label LIKE '%BALANCE B/F%'
            AND item_value IS NOT NULL AND item_value != ''
            ORDER BY employee_id, item_label
        """, (session_id,))
        
        loan_items = cursor.fetchall()
        print(f"   📊 Found {len(loan_items)} Balance B/F items to analyze")
        
        new_loans_detected = []
        
        for employee_id, item_label, item_value in loan_items:
            # Extract loan type from item label
            if ' - BALANCE B/F' in item_label:
                loan_type = item_label.replace(' - BALANCE B/F', '').strip()
                
                # Get complete loan calculation data
                loan_data = get_loan_calculation_data(cursor, employee_id, session_id, loan_type)
                loan_data['balance_bf'] = extract_numeric_value(item_value)
                
                # Apply enhanced detection
                is_new, reason = is_new_loan_enhanced(cursor, employee_id, session_id, loan_type, loan_data)
                
                if is_new:
                    # Get employee details
                    cursor.execute("""
                        SELECT employee_name FROM employees
                        WHERE employee_id = ? AND session_id = ?
                        ORDER BY created_at DESC LIMIT 1
                    """, (employee_id, session_id))
                    
                    emp_result = cursor.fetchone()
                    employee_name = emp_result[0] if emp_result else employee_id
                    
                    # Get department
                    department = get_employee_department_from_db(cursor, employee_id, session_id)
                    
                    # Classify loan
                    loan_classification = classify_loan_type(loan_type)
                    
                    new_loan = {
                        'employee_id': employee_id,
                        'employee_name': employee_name,
                        'department': department,
                        'loan_type': loan_type,
                        'loan_amount': loan_data['balance_bf'],
                        'classification': loan_classification,
                        'reason': reason
                    }
                    
                    new_loans_detected.append(new_loan)
        
        print(f"   🎯 Enhanced detection found {len(new_loans_detected)} NEW loans")
        
        # Insert new loans into appropriate tables
        in_house_count = 0
        external_count = 0
        
        for loan in new_loans_detected:
            try:
                if loan['classification'] == 'IN-HOUSE':
                    # Check for duplicates
                    cursor.execute("""
                        SELECT COUNT(*) FROM in_house_loans
                        WHERE employee_no = ? AND loan_type = ? AND source_session = ?
                    """, (loan['employee_id'], loan['loan_type'], session_id))
                    
                    if cursor.fetchone()[0] == 0:
                        cursor.execute("""
                            INSERT INTO in_house_loans
                            (employee_no, employee_name, department, loan_type, loan_amount,
                             period_month, period_year, period_acquired, source_session, remarks)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, (
                            loan['employee_id'], loan['employee_name'], loan['department'],
                            loan['loan_type'], loan['loan_amount'],
                            '06', '2025', '2025-06', session_id,
                            f"Enhanced detection: {loan['reason']}"
                        ))
                        in_house_count += 1
                else:
                    # Check for duplicates
                    cursor.execute("""
                        SELECT COUNT(*) FROM external_loans
                        WHERE employee_no = ? AND loan_type = ? AND source_session = ?
                    """, (loan['employee_id'], loan['loan_type'], session_id))
                    
                    if cursor.fetchone()[0] == 0:
                        cursor.execute("""
                            INSERT INTO external_loans
                            (employee_no, employee_name, department, loan_type, loan_amount,
                             period_month, period_year, period_acquired, source_session, remarks)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, (
                            loan['employee_id'], loan['employee_name'], loan['department'],
                            loan['loan_type'], loan['loan_amount'],
                            '06', '2025', '2025-06', session_id,
                            f"Enhanced detection: {loan['reason']}"
                        ))
                        external_count += 1
                        
            except Exception as e:
                print(f"   ⚠️ Error inserting loan {loan['loan_type']} for {loan['employee_id']}: {e}")
        
        conn.commit()
        
        print(f"   ✅ Added {in_house_count} in-house loans")
        print(f"   ✅ Added {external_count} external loans")
        print(f"   🎯 Total new loans added: {in_house_count + external_count}")
        
        return in_house_count + external_count
        
    except Exception as e:
        print(f"   ❌ Error enhancing loan detection: {e}")
        return 0

def verify_enhanced_detection(cursor, session_id):
    """Verify the results of enhanced detection"""
    print(f"\n🔍 VERIFYING ENHANCED DETECTION RESULTS:")
    
    try:
        # Get counts before and after
        cursor.execute("SELECT COUNT(*) FROM in_house_loans WHERE source_session = ?", (session_id,))
        in_house_total = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM external_loans WHERE source_session = ?", (session_id,))
        external_total = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM motor_vehicle_maintenance WHERE source_session = ?", (session_id,))
        motor_total = cursor.fetchone()[0]
        
        print(f"   📊 Final counts:")
        print(f"      In-house loans: {in_house_total}")
        print(f"      External loans: {external_total}")
        print(f"      Motor vehicle: {motor_total}")
        print(f"      Total tracked: {in_house_total + external_total + motor_total}")
        
        # Show sample of enhanced detections
        cursor.execute("""
            SELECT employee_no, loan_type, loan_amount, remarks
            FROM in_house_loans
            WHERE source_session = ? AND remarks LIKE '%Enhanced detection%'
            ORDER BY loan_amount DESC LIMIT 5
        """, (session_id,))
        
        enhanced_samples = cursor.fetchall()
        if enhanced_samples:
            print(f"\n   🎯 Sample enhanced in-house detections:")
            for emp_no, loan_type, amount, remarks in enhanced_samples:
                print(f"      {emp_no}: {loan_type} = {amount} ({remarks})")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Verification failed: {e}")
        return False

def main():
    """Main function to enhance loan detection"""
    print("🚀 ENHANCED LOAN DETECTION - PRODUCTION SOLUTION")
    print("=" * 60)
    
    # Get database path
    db_path = get_database_path()
    print(f"📁 Database: {db_path}")
    
    if not os.path.exists(db_path):
        print(f"❌ Database not found at: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get current session
        cursor.execute("SELECT session_id FROM payroll_sessions ORDER BY created_at DESC LIMIT 1")
        session_result = cursor.fetchone()
        
        if not session_result:
            print("❌ No payroll sessions found")
            return False
        
        session_id = session_result[0]
        print(f"🎯 Working with session: {session_id}")
        
        # Apply enhanced detection
        new_loans_added = enhance_loan_detection_for_session(cursor, conn, session_id)
        
        # Verify results
        verification_passed = verify_enhanced_detection(cursor, session_id)
        
        conn.close()
        
        print(f"\n✅ ENHANCED LOAN DETECTION COMPLETED!")
        print(f"🎯 New loans added: {new_loans_added}")
        
        if verification_passed:
            print("✅ Enhanced detection successful!")
        else:
            print("⚠️ Some issues in verification - manual review recommended")
        
        return verification_passed
        
    except Exception as e:
        print(f"❌ Enhancement failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
