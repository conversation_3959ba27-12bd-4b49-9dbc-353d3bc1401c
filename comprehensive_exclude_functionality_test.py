#!/usr/bin/env python3
"""
Comprehensive EXCLUDE Functionality Test
Exclusive test of the "INCLUDE IN REPORT" toggle for EXCLUDE scenarios
"""

import sys
import os
import sqlite3
import json
import time
from typing import Dict, List, Any, <PERSON><PERSON>

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def setup_test_exclusions(cursor, conn) -> List[Tuple[str, str]]:
    """Setup test items for exclusion testing"""
    print("🔧 SETTING UP TEST EXCLUSIONS:")
    
    # Find items to exclude for testing
    cursor.execute("""
        SELECT DISTINCT section_name, item_label 
        FROM comparison_results 
        WHERE session_id = (SELECT session_id FROM comparison_results LIMIT 1)
        LIMIT 10
    """)
    
    test_items = cursor.fetchall()
    excluded_items = []
    
    if test_items:
        # Select 3 items to exclude for testing
        items_to_exclude = test_items[:3]
        
        for section, item in items_to_exclude:
            # Check if item exists in dictionary
            cursor.execute("""
                SELECT id FROM dictionary_items 
                WHERE item_name = ? AND section_id = (
                    SELECT id FROM dictionary_sections WHERE section_name = ?
                )
            """, (item, section))
            
            existing = cursor.fetchone()
            
            if existing:
                # Update existing item to exclude
                cursor.execute("""
                    UPDATE dictionary_items 
                    SET include_in_report = 0 
                    WHERE id = ?
                """, (existing[0],))
                excluded_items.append((section, item))
                print(f"   ✅ Excluded existing item: {section}.{item}")
            else:
                # Insert new item as excluded
                cursor.execute("""
                    INSERT OR IGNORE INTO dictionary_sections (section_name) 
                    VALUES (?)
                """, (section,))
                
                cursor.execute("""
                    SELECT id FROM dictionary_sections WHERE section_name = ?
                """, (section,))
                section_id = cursor.fetchone()[0]
                
                cursor.execute("""
                    INSERT INTO dictionary_items (item_name, section_id, include_in_report) 
                    VALUES (?, ?, 0)
                """, (item, section_id))
                excluded_items.append((section, item))
                print(f"   ✅ Created excluded item: {section}.{item}")
        
        conn.commit()
        print(f"   📊 Total items excluded for testing: {len(excluded_items)}")
    else:
        print("   ⚠️ No comparison results found for exclusion testing")
    
    return excluded_items

def test_dictionary_toggle_functionality(cursor, excluded_items: List[Tuple[str, str]]) -> bool:
    """Test 1: Dictionary Toggle Test"""
    print("\n1. 🔘 DICTIONARY TOGGLE TEST:")
    
    try:
        sys.path.append(os.path.dirname(__file__))
        from core.dictionary_manager import PayrollDictionaryManager
        
        manager = PayrollDictionaryManager(debug=False)
        
        all_tests_passed = True
        
        for section, item in excluded_items:
            result = manager.should_include_in_report(section, item)
            
            if result == False:
                print(f"   ✅ {section}.{item}: Correctly EXCLUDED")
            else:
                print(f"   ❌ {section}.{item}: Should be EXCLUDED but returned {result}")
                all_tests_passed = False
        
        # Test a few items that should be included
        cursor.execute("""
            SELECT di.item_name, ds.section_name
            FROM dictionary_items di
            JOIN dictionary_sections ds ON di.section_id = ds.id
            WHERE di.include_in_report = 1
            LIMIT 3
        """)
        
        included_items = cursor.fetchall()
        
        for item, section in included_items:
            result = manager.should_include_in_report(section, item)
            
            if result == True:
                print(f"   ✅ {section}.{item}: Correctly INCLUDED")
            else:
                print(f"   ❌ {section}.{item}: Should be INCLUDED but returned {result}")
                all_tests_passed = False
        
        return all_tests_passed
        
    except Exception as e:
        print(f"   ❌ Dictionary toggle test failed: {e}")
        return False

def test_comparison_results_exclusion(cursor, excluded_items: List[Tuple[str, str]]) -> Tuple[bool, int, int]:
    """Test 2: Comparison Results Processing Exclusion"""
    print("\n2. ⚖️ COMPARISON RESULTS PROCESSING EXCLUSION TEST:")
    
    try:
        # Get current session
        from core.session_manager import get_current_session_id
        current_session = get_current_session_id()
        
        # Count total comparison results
        cursor.execute("""
            SELECT COUNT(*) FROM comparison_results 
            WHERE session_id = ?
        """, (current_session,))
        total_results = cursor.fetchone()[0]
        
        # Count excluded items in comparison results
        excluded_count = 0
        for section, item in excluded_items:
            cursor.execute("""
                SELECT COUNT(*) FROM comparison_results cr
                WHERE cr.session_id = ? AND cr.section_name = ? AND cr.item_label = ?
            """, (current_session, section, item))
            
            item_count = cursor.fetchone()[0]
            excluded_count += item_count
            
            if item_count > 0:
                print(f"   ⚠️ {section}.{item}: Found {item_count} results (should be filtered)")
            else:
                print(f"   ✅ {section}.{item}: No results found (correctly filtered)")
        
        print(f"   📊 Total comparison results: {total_results}")
        print(f"   📊 Excluded items in results: {excluded_count}")
        
        # Test with filtering applied
        filtered_count = 0
        for section, item in excluded_items:
            cursor.execute("""
                SELECT COUNT(*) FROM comparison_results cr
                LEFT JOIN dictionary_items di ON cr.item_label = di.item_name
                WHERE cr.session_id = ? AND cr.section_name = ? AND cr.item_label = ?
                  AND (di.include_in_report = 1 OR di.include_in_report IS NULL)
            """, (current_session, section, item))
            
            filtered_item_count = cursor.fetchone()[0]
            filtered_count += filtered_item_count
        
        print(f"   📊 Excluded items after filtering: {filtered_count}")
        
        return filtered_count == 0, total_results, excluded_count
        
    except Exception as e:
        print(f"   ❌ Comparison results exclusion test failed: {e}")
        return False, 0, 0

def test_pre_reporting_phase_exclusion(cursor, excluded_items: List[Tuple[str, str]]) -> bool:
    """Test 3: Pre-reporting Phase Data Exclusion"""
    print("\n3. 📋 PRE-REPORTING PHASE DATA EXCLUSION TEST:")
    
    try:
        from core.session_manager import get_current_session_id
        current_session = get_current_session_id()
        
        # Check pre-reporting results
        cursor.execute("""
            SELECT COUNT(*) FROM pre_reporting_results pr
            JOIN comparison_results cr ON pr.change_id = cr.id
            WHERE cr.session_id = ?
        """, (current_session,))
        
        total_pre_reporting = cursor.fetchone()[0]
        
        excluded_in_pre_reporting = 0
        for section, item in excluded_items:
            cursor.execute("""
                SELECT COUNT(*) FROM pre_reporting_results pr
                JOIN comparison_results cr ON pr.change_id = cr.id
                WHERE cr.session_id = ? AND cr.section_name = ? AND cr.item_label = ?
            """, (current_session, section, item))
            
            item_count = cursor.fetchone()[0]
            excluded_in_pre_reporting += item_count
            
            if item_count > 0:
                print(f"   ⚠️ {section}.{item}: Found {item_count} pre-reporting results")
            else:
                print(f"   ✅ {section}.{item}: No pre-reporting results (correctly filtered)")
        
        print(f"   📊 Total pre-reporting results: {total_pre_reporting}")
        print(f"   📊 Excluded items in pre-reporting: {excluded_in_pre_reporting}")
        
        return excluded_in_pre_reporting == 0
        
    except Exception as e:
        print(f"   ❌ Pre-reporting phase exclusion test failed: {e}")
        return False

def test_final_report_generation_exclusion(excluded_items: List[Tuple[str, str]]) -> bool:
    """Test 4: Final Report Generation Exclusion"""
    print("\n4. 📄 FINAL REPORT GENERATION EXCLUSION TEST:")
    
    try:
        from core.advanced_reporting_system import AdvancedReportingSystem
        
        reporting_system = AdvancedReportingSystem()
        
        # Create test data with excluded items
        test_data = {
            'comparison_results': []
        }
        
        # Add excluded items to test data
        for section, item in excluded_items:
            test_data['comparison_results'].append({
                'section_name': section,
                'item_label': item,
                'employee_id': 'TEST001',
                'employee_name': 'Test Employee',
                'previous_value': '100',
                'current_value': '200',
                'change_type': 'modified'
            })
        
        # Add some included items
        test_data['comparison_results'].extend([
            {
                'section_name': 'EARNINGS',
                'item_label': 'BASIC SALARY',
                'employee_id': 'TEST002',
                'employee_name': 'Test Employee 2',
                'previous_value': '1000',
                'current_value': '1100',
                'change_type': 'modified'
            },
            {
                'section_name': 'DEDUCTIONS',
                'item_label': 'INCOME TAX',
                'employee_id': 'TEST003',
                'employee_name': 'Test Employee 3',
                'previous_value': '100',
                'current_value': '110',
                'change_type': 'modified'
            }
        ])
        
        print(f"   📊 Test data before filtering: {len(test_data['comparison_results'])} items")
        
        # Apply filtering
        filtered_data = reporting_system._apply_include_in_report_filter(test_data)
        
        print(f"   📊 Test data after filtering: {len(filtered_data['comparison_results'])} items")
        
        # Check if excluded items are present
        excluded_found = 0
        for item in filtered_data['comparison_results']:
            for section, excluded_item in excluded_items:
                if item['section_name'] == section and item['item_label'] == excluded_item:
                    excluded_found += 1
                    print(f"   ❌ {section}.{excluded_item}: Found in filtered data (should be excluded)")
        
        if excluded_found == 0:
            print(f"   ✅ All excluded items properly filtered from report generation")
            return True
        else:
            print(f"   ❌ {excluded_found} excluded items found in filtered data")
            return False
        
    except Exception as e:
        print(f"   ❌ Final report generation exclusion test failed: {e}")
        return False

def test_ui_display_exclusion(excluded_items: List[Tuple[str, str]]) -> bool:
    """Test 5: UI Display of Reportable Items Exclusion"""
    print("\n5. 🖥️ UI DISPLAY OF REPORTABLE ITEMS EXCLUSION TEST:")
    
    try:
        from core.phased_process_manager import PhasedProcessManager
        from core.session_manager import get_current_session_id
        
        manager = PhasedProcessManager(debug_mode=False)
        manager.session_id = get_current_session_id()
        
        # Test the method that loads data for reporting (used by UI)
        selected_changes = manager._load_selected_changes_for_reporting()
        
        print(f"   📊 Selected changes for reporting: {len(selected_changes)}")
        
        # Check if excluded items are present
        excluded_found = 0
        for change in selected_changes:
            for section, excluded_item in excluded_items:
                if change['section_name'] == section and change['item_label'] == excluded_item:
                    excluded_found += 1
                    print(f"   ❌ {section}.{excluded_item}: Found in UI data (should be excluded)")
        
        if excluded_found == 0:
            print(f"   ✅ All excluded items properly filtered from UI display")
            return True
        else:
            print(f"   ❌ {excluded_found} excluded items found in UI data")
            return False
        
    except Exception as e:
        print(f"   ❌ UI display exclusion test failed: {e}")
        return False

def measure_performance_impact(cursor, excluded_items: List[Tuple[str, str]]) -> Dict[str, float]:
    """Test 6: Performance Impact Measurement"""
    print("\n6. ⚡ PERFORMANCE IMPACT MEASUREMENT:")
    
    performance_results = {}
    
    try:
        from core.dictionary_manager import PayrollDictionaryManager
        from core.advanced_reporting_system import AdvancedReportingSystem
        
        manager = PayrollDictionaryManager(debug=False)
        reporting_system = AdvancedReportingSystem()
        
        # Test 1: Dictionary lookup performance
        print("   Testing dictionary lookup performance...")
        
        start_time = time.time()
        for i in range(1000):
            for section, item in excluded_items:
                manager.should_include_in_report(section, item)
        lookup_time = time.time() - start_time
        
        performance_results['dictionary_lookup_1000_calls'] = lookup_time
        print(f"   📊 Dictionary lookup (1000 calls): {lookup_time:.4f}s")
        
        # Test 2: Report filtering performance with exclusions
        print("   Testing report filtering performance...")
        
        # Create large test dataset
        large_test_data = {
            'comparison_results': []
        }
        
        # Add excluded items (should be filtered out)
        for i in range(500):
            for section, item in excluded_items:
                large_test_data['comparison_results'].append({
                    'section_name': section,
                    'item_label': item,
                    'employee_id': f'EMP{i:03d}',
                    'value': str(i * 100)
                })
        
        # Add included items
        for i in range(500):
            large_test_data['comparison_results'].append({
                'section_name': 'EARNINGS',
                'item_label': 'BASIC SALARY',
                'employee_id': f'EMP{i:03d}',
                'value': str(i * 100)
            })
        
        print(f"   📊 Test dataset size: {len(large_test_data['comparison_results'])} items")
        
        start_time = time.time()
        filtered_data = reporting_system._apply_include_in_report_filter(large_test_data)
        filtering_time = time.time() - start_time
        
        performance_results['report_filtering_large_dataset'] = filtering_time
        print(f"   📊 Report filtering time: {filtering_time:.4f}s")
        print(f"   📊 Items after filtering: {len(filtered_data['comparison_results'])}")
        
        # Calculate filtering efficiency
        original_count = len(large_test_data['comparison_results'])
        filtered_count = len(filtered_data['comparison_results'])
        reduction_percentage = ((original_count - filtered_count) / original_count) * 100
        
        performance_results['data_reduction_percentage'] = reduction_percentage
        print(f"   📊 Data reduction: {reduction_percentage:.1f}%")
        
        return performance_results
        
    except Exception as e:
        print(f"   ❌ Performance impact measurement failed: {e}")
        return {}

def analyze_optimal_filter_placement() -> Dict[str, Any]:
    """Test 7: Data Flow Tracing and Optimal Filter Placement Analysis"""
    print("\n7. 🔍 DATA FLOW TRACING & OPTIMAL FILTER PLACEMENT ANALYSIS:")
    
    analysis_results = {
        'extraction_phase': {'feasible': False, 'impact': 'highest', 'complexity': 'high'},
        'comparison_phase': {'feasible': True, 'impact': 'high', 'complexity': 'medium'},
        'pre_reporting_phase': {'feasible': True, 'impact': 'medium', 'complexity': 'low'},
        'report_generation': {'feasible': True, 'impact': 'low', 'complexity': 'low'}
    }
    
    print("   📊 FILTER PLACEMENT ANALYSIS:")
    
    # Extraction Phase Analysis
    print("   🔸 EXTRACTION PHASE:")
    print("     - Pros: Maximum processing time savings, earliest filtering")
    print("     - Cons: Complex integration, affects core data extraction logic")
    print("     - Impact: Highest (filters before any processing)")
    print("     - Complexity: High (requires core extraction logic changes)")
    print("     - Recommendation: Not recommended due to complexity vs benefit")
    
    # Comparison Phase Analysis
    print("   🔸 COMPARISON PHASE:")
    print("     - Pros: Significant processing savings, logical placement")
    print("     - Cons: Requires comparison logic modification")
    print("     - Impact: High (filters before expensive comparison operations)")
    print("     - Complexity: Medium (requires comparison query modifications)")
    print("     - Recommendation: OPTIMAL - Best balance of impact and complexity")
    
    # Pre-reporting Phase Analysis
    print("   🔸 PRE-REPORTING PHASE:")
    print("     - Pros: Easy implementation, good processing savings")
    print("     - Cons: Some processing already done on excluded items")
    print("     - Impact: Medium (filters after comparison but before reporting)")
    print("     - Complexity: Low (simple query modifications)")
    print("     - Recommendation: Good alternative if comparison phase is too complex")
    
    # Report Generation Analysis
    print("   🔸 REPORT GENERATION PHASE:")
    print("     - Pros: Easy implementation, no impact on core logic")
    print("     - Cons: Minimal processing savings, late filtering")
    print("     - Impact: Low (most processing already done)")
    print("     - Complexity: Low (already implemented)")
    print("     - Recommendation: Current implementation - minimal benefit")
    
    return analysis_results

def comprehensive_exclude_functionality_test():
    """Main test function for comprehensive EXCLUDE functionality testing"""
    print("🔍 COMPREHENSIVE EXCLUDE FUNCTIONALITY TEST")
    print("=" * 70)
    
    test_results = {
        'dictionary_toggle': False,
        'comparison_exclusion': False,
        'pre_reporting_exclusion': False,
        'report_generation_exclusion': False,
        'ui_display_exclusion': False,
        'performance_measured': False,
        'optimal_placement_analyzed': False
    }
    
    performance_data = {}
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return test_results, performance_data
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Setup test exclusions
        excluded_items = setup_test_exclusions(cursor, conn)
        
        if not excluded_items:
            print("❌ Could not setup test exclusions")
            return test_results, performance_data
        
        # Test 1: Dictionary Toggle Functionality
        test_results['dictionary_toggle'] = test_dictionary_toggle_functionality(cursor, excluded_items)
        
        # Test 2: Comparison Results Processing Exclusion
        comparison_result, total_results, excluded_count = test_comparison_results_exclusion(cursor, excluded_items)
        test_results['comparison_exclusion'] = comparison_result
        
        # Test 3: Pre-reporting Phase Data Exclusion
        test_results['pre_reporting_exclusion'] = test_pre_reporting_phase_exclusion(cursor, excluded_items)
        
        # Test 4: Final Report Generation Exclusion
        test_results['report_generation_exclusion'] = test_final_report_generation_exclusion(excluded_items)
        
        # Test 5: UI Display of Reportable Items Exclusion
        test_results['ui_display_exclusion'] = test_ui_display_exclusion(excluded_items)
        
        # Test 6: Performance Impact Measurement
        performance_data = measure_performance_impact(cursor, excluded_items)
        test_results['performance_measured'] = len(performance_data) > 0
        
        # Test 7: Optimal Filter Placement Analysis
        placement_analysis = analyze_optimal_filter_placement()
        test_results['optimal_placement_analyzed'] = len(placement_analysis) > 0
        
        # Cleanup - restore original state
        print("\n🔧 CLEANUP - RESTORING ORIGINAL STATE:")
        for section, item in excluded_items:
            cursor.execute("""
                UPDATE dictionary_items 
                SET include_in_report = 1 
                WHERE item_name = ?
            """, (item,))
        conn.commit()
        print("   ✅ Restored all test items to INCLUDE state")
        
        conn.close()
        
        # Final Results Summary
        print("\n8. 📋 COMPREHENSIVE TEST RESULTS SUMMARY:")
        print("   " + "=" * 60)
        
        passed_tests = sum(test_results.values())
        total_tests = len(test_results)
        
        for test_name, result in test_results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"   {test_name.replace('_', ' ').title()}: {status}")
        
        print("   " + "=" * 60)
        print(f"   OVERALL RESULT: {passed_tests}/{total_tests} tests passed")
        
        if performance_data:
            print("\n   📊 PERFORMANCE METRICS:")
            for metric, value in performance_data.items():
                if isinstance(value, float):
                    if 'percentage' in metric:
                        print(f"   {metric.replace('_', ' ').title()}: {value:.1f}%")
                    else:
                        print(f"   {metric.replace('_', ' ').title()}: {value:.4f}s")
        
        print("\n   💡 RECOMMENDATIONS:")
        if passed_tests == total_tests:
            print("   🎯 EXCLUDE functionality: FULLY OPERATIONAL")
            print("   🚀 Optimal filter placement: COMPARISON PHASE (recommended)")
            print("   ⚡ Performance impact: Significant data reduction achieved")
        else:
            print("   ⚠️ EXCLUDE functionality: Some issues found")
            print("   🔧 Address failed tests before production deployment")
        
        return test_results, performance_data
        
    except Exception as e:
        print(f"❌ Critical error during comprehensive testing: {e}")
        import traceback
        traceback.print_exc()
        return test_results, performance_data

if __name__ == "__main__":
    results, performance = comprehensive_exclude_functionality_test()
    
    # Exit with appropriate code
    passed_tests = sum(results.values())
    total_tests = len(results)
    
    if passed_tests == total_tests:
        sys.exit(0)  # All tests passed
    else:
        sys.exit(1)  # Some tests failed
