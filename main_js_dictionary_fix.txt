
// PRODUCTION FIX: Replace the problematic fallback in main.js around line 1717-1721
// Replace this code:
//   const result = await runHybridScript(pythonPath, ['save-dictionary', JSON.stringify(dictionary)]);

// With this improved version:
try {
  console.log('[DICTIONARY] Using improved file-based fallback...');
  
  // Write dictionary to temporary file
  const tempFile = path.join(os.tmpdir(), `dictionary_${Date.now()}.json`);
  fs.writeFileSync(tempFile, JSON.stringify(dictionary), 'utf8');
  
  // Use file-based fallback script
  const fallbackPath = path.join(__dirname, 'core', 'dictionary_save_fallback.py');
  const result = await runHybridScript(fallbackPath, [tempFile]);
  
  return result.trim() === 'true';
} catch (fallbackError) {
  console.error('[DICTIONARY] ❌ Improved fallback also failed:', fallbackError);
  return false;
}
