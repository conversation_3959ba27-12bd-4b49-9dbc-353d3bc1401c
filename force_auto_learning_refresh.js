// EMERGENCY AUTO-LEARNING REFRESH SCRIPT
// Copy and paste this entire script into the browser console in the Auto-Learning tab

console.log('🚀 EMERGENCY AUTO-LEARNING REFRESH SCRIPT STARTING...');

// Override the loadPendingItems function with our fixed version
window.loadPendingItems = async function() {
  try {
    console.log('🔄 [EMERGENCY] Loading pending items from API...');
    console.log('🔄 [EMERGENCY] Timestamp:', new Date().toISOString());
    
    // Check if window.api is available
    if (!window.api || !window.api.getPendingItems) {
      console.error('❌ [EMERGENCY] window.api not available');
      return;
    }

    const result = await window.api.getPendingItems();
    console.log('📊 [EMERGENCY] API Response received:', result);
    console.log('📊 [EMERGENCY] Response type:', typeof result);
    console.log('📊 [EMERGENCY] Response keys:', Object.keys(result || {}));

    // Handle both direct array and object with data property
    let pendingItems;
    if (Array.isArray(result)) {
      pendingItems = result;
    } else {
      // PRODUCTION FIX: Handle the actual API response structure
      pendingItems = result.data || result.pending_items || [];
    }

    console.log('📊 [EMERGENCY] Raw API result:', result);
    console.log('📊 [EMERGENCY] Extracted pending items count:', pendingItems.length);
    console.log('📊 [EMERGENCY] First 3 extracted items:', pendingItems.slice(0, 3));

    // Normalize the data structure - ensure all items have consistent properties
    pendingItems = pendingItems.map((item, index) => {
      if (!item || typeof item !== 'object') {
        console.warn(`❌ [EMERGENCY] Invalid item at index ${index}:`, item);
        return null;
      }

      // PRODUCTION FIX: Handle the actual field names returned by the API
      return {
        id: item.id || item.discovery_id || `item_${index}_${Date.now()}`,
        item_name: item.item_label || item.item_name || item.label || 'Unknown',
        section: item.section_name || item.section || item.suggested_section || 'EARNINGS',
        value: item.value || 'N/A',
        confidence: typeof item.confidence_score === 'number' ? item.confidence_score :
                   (typeof item.confidence === 'number' ? item.confidence : 0),
        source: item.source || 'Auto-detected',
        auto_approved: item.auto_approved || false,
        is_new_item: item.is_new_item !== false,
        status: item.status || 'pending_approval',
        timestamp: item.created_at || item.timestamp || item.extracted_timestamp || new Date().toISOString()
      };
    }).filter(item => item !== null); // Remove any null items

    console.log('📊 [EMERGENCY] Final mapped items count:', pendingItems.length);
    console.log('📊 [EMERGENCY] Sample mapped items:', pendingItems.slice(0, 3));

    // Store in global variable
    window.pendingItems = pendingItems;

    // Update stats - calculate from actual data if not provided
    const stats = result.stats || {
      pending_count: pendingItems.length,
      auto_added_today: pendingItems.filter(item => item.auto_approved).length,
      learning_accuracy: '100%',
      status: 'Ready'
    };
    
    console.log('📊 [EMERGENCY] Calculated stats:', stats);
    
    // Update stats in UI
    updateAutoLearningStats(stats);

    // Render pending items
    console.log('🎯 [EMERGENCY] About to render', pendingItems.length, 'pending items');
    renderPendingItems();

    console.log('✅ [EMERGENCY] Successfully loaded and rendered', pendingItems.length, 'pending items');

  } catch (error) {
    console.error('❌ [EMERGENCY] Error loading pending items:', error);
  }
};

// Override the renderPendingItems function with enhanced debugging
window.renderPendingItems = function() {
  console.log('🎨 [EMERGENCY] renderPendingItems called with', (window.pendingItems || []).length, 'items');
  
  const pendingItemsList = document.getElementById('pending-items-list');
  if (!pendingItemsList) {
    console.error('❌ [EMERGENCY] pending-items-list element not found');
    return;
  }

  pendingItemsList.innerHTML = '';

  if (!window.pendingItems || window.pendingItems.length === 0) {
    console.log('📋 [EMERGENCY] No pending items to display');
    pendingItemsList.innerHTML = `
      <tr>
        <td colspan="6" class="no-items">No pending items for approval</td>
      </tr>
    `;
    return;
  }

  console.log('📋 [EMERGENCY] Rendering pending items:', window.pendingItems);

  window.pendingItems.forEach((item, index) => {
    try {
      // Validate item structure
      if (!item || typeof item !== 'object') {
        console.warn(`⚠️ [EMERGENCY] Invalid item at index ${index}:`, item);
        return;
      }

      const itemId = item.id || `item_${index}`;
      const itemName = item.item_name || item.label || 'Unknown';
      const itemSection = item.section || item.suggested_section || 'EARNINGS';
      const itemValue = item.value || 'N/A';
      const itemConfidence = typeof item.confidence === 'number' ? item.confidence : 0;
      const itemSource = item.source || 'Auto-detected';

      // Create table row
      const row = document.createElement('tr');
      row.setAttribute('data-item-id', itemId);

      // Get confidence class
      const getConfidenceClass = (confidence) => {
        if (confidence >= 0.8) return 'confidence-high';
        if (confidence >= 0.5) return 'confidence-medium';
        return 'confidence-low';
      };

      row.innerHTML = `
        <td class="item-name">${itemName}</td>
        <td class="suggested-section">
          <select class="section-select" data-item-id="${itemId}">
            <option value="EARNINGS" ${itemSection === 'EARNINGS' ? 'selected' : ''}>Earnings</option>
            <option value="DEDUCTIONS" ${itemSection === 'DEDUCTIONS' ? 'selected' : ''}>Deductions</option>
            <option value="LOANS" ${itemSection === 'LOANS' ? 'selected' : ''}>Loans</option>
            <option value="PERSONAL DETAILS" ${itemSection === 'PERSONAL DETAILS' ? 'selected' : ''}>Personal Details</option>
            <option value="EMPLOYERS CONTRIBUTION" ${itemSection === 'EMPLOYERS CONTRIBUTION' ? 'selected' : ''}>Employer Contributions</option>
            <option value="EMPLOYEE BANK DETAILS" ${itemSection === 'EMPLOYEE BANK DETAILS' ? 'selected' : ''}>Bank Details</option>
          </select>
        </td>
        <td class="item-value">${itemValue}</td>
        <td class="confidence">
          <span class="confidence-badge ${getConfidenceClass(itemConfidence)}">${Math.round(itemConfidence * 100)}%</span>
        </td>
        <td class="source">${itemSource}</td>
        <td class="actions">
          <button class="btn success btn-sm" onclick="approvePendingItem('${itemId}')">
            <i class="fas fa-check"></i> Approve
          </button>
          <button class="btn danger btn-sm" onclick="rejectPendingItem('${itemId}')">
            <i class="fas fa-times"></i> Reject
          </button>
        </td>
      `;
      pendingItemsList.appendChild(row);
    } catch (error) {
      console.error(`❌ [EMERGENCY] Error rendering item at index ${index}:`, error, item);
    }
  });

  console.log('✅ [EMERGENCY] Successfully rendered', window.pendingItems.length, 'items');
};

// Override updateAutoLearningStats function
window.updateAutoLearningStats = function(stats) {
  console.log('📊 [EMERGENCY] Updating stats:', stats);
  
  // Update pending count
  const pendingCountElement = document.querySelector('.stat-card .stat-number');
  if (pendingCountElement) {
    pendingCountElement.textContent = stats.pending_count || 0;
  }
  
  // Update other stats if elements exist
  const autoAddedElement = document.querySelector('.stat-card:nth-child(2) .stat-number');
  if (autoAddedElement) {
    autoAddedElement.textContent = stats.auto_added_today || 0;
  }
  
  const accuracyElement = document.querySelector('.stat-card:nth-child(3) .stat-number');
  if (accuracyElement) {
    accuracyElement.textContent = stats.learning_accuracy || '100%';
  }
  
  const statusElement = document.querySelector('.stat-card:nth-child(4) .stat-number');
  if (statusElement) {
    statusElement.textContent = stats.status || 'Ready';
  }
  
  console.log('✅ [EMERGENCY] Stats updated successfully');
};

// Force refresh function
window.forceEmergencyRefresh = function() {
  console.log('🚀 [EMERGENCY] FORCE REFRESH TRIGGERED');
  console.log('🔄 [EMERGENCY] Clearing cache and reloading...');
  
  // Clear any cached data
  window.pendingItems = [];
  
  // Force reload
  window.loadPendingItems();
  
  console.log('✅ [EMERGENCY] Emergency force refresh completed');
};

console.log('✅ EMERGENCY AUTO-LEARNING REFRESH SCRIPT LOADED');
console.log('🎯 Now execute: forceEmergencyRefresh()');
