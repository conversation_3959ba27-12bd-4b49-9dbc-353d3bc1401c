#!/usr/bin/env python3
"""
Test Pre-reporting Method Directly
"""

import sys
import os
import json

def test_pre_reporting_method():
    """Test the pre-reporting method directly"""
    print("🔍 TESTING PRE-REPORTING METHOD DIRECTLY")
    print("=" * 50)
    
    try:
        sys.path.append(os.path.dirname(__file__))
        from core.phased_process_manager import PhasedProcessManager
        
        # Test with debug mode to see what's happening
        manager = PhasedProcessManager(debug_mode=True)
        
        print("1. Testing get_pre_reporting_data() with no parameters...")
        result = manager.get_pre_reporting_data()
        
        print(f"Result type: {type(result)}")
        print(f"Result: {result}")
        
        if isinstance(result, dict):
            print(f"Success: {result.get('success')}")
            print(f"Error: {result.get('error')}")
            print(f"Data count: {len(result.get('data', []))}")
            print(f"Session ID: {result.get('session_id')}")
        
        # Test with explicit session
        print("\n2. Testing with explicit session...")
        
        # Get current session
        try:
            from core.session_manager import get_current_session_id
            current_session = get_current_session_id()
            print(f"Current session: {current_session}")
            
            if current_session:
                result2 = manager.get_pre_reporting_data(current_session)
                print(f"Explicit session result: {result2}")
        except Exception as e:
            print(f"Session manager error: {e}")
        
        # Test JSON serialization
        print("\n3. Testing JSON serialization...")
        try:
            json_result = json.dumps(result)
            print(f"JSON serialization successful: {len(json_result)} characters")
        except Exception as e:
            print(f"JSON serialization failed: {e}")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_pre_reporting_method()
