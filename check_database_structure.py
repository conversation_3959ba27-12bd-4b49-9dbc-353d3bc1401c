#!/usr/bin/env python3
"""
Check database structure and tables
"""

import sqlite3
import os

def main():
    db_path = 'payroll_audit.db'
    
    if not os.path.exists(db_path):
        print(f"Database not found: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        print(f"Tables in database: {tables}")
        
        # Check if dictionary_items exists
        if 'dictionary_items' in tables:
            cursor.execute("PRAGMA table_info(dictionary_items)")
            columns = cursor.fetchall()
            print("\ndictionary_items columns:")
            for col in columns:
                print(f"  {col[1]} {col[2]} (default: {col[4]})")
        else:
            print("\ndictionary_items table does not exist")
        
        conn.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
