#!/usr/bin/env python3
"""
VERIFICATION: Auto-Learning Workflow Fixes
Comprehensive verification of both Dictionary Manager integration and Reject All functionality
"""

import sys
import os
import sqlite3
import json
import subprocess

def verify_auto_learning_fixes():
    """Verify that both Auto-Learning workflow issues are fixed"""
    print("🔍 VERIFICATION: AUTO-LEARNING WORKFLOW FIXES")
    print("=" * 55)
    
    try:
        # 1. VERIFY ISSUE 1: Dictionary Manager Integration
        print("\n1. ✅ VERIFYING ISSUE 1: Dictionary Manager Integration")
        
        # Connect to database
        db_path = r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check approved items in auto_learning_results
        cursor.execute("""
            SELECT COUNT(*) 
            FROM auto_learning_results 
            WHERE auto_approved = 1 AND dictionary_updated = 1
        """)
        approved_items = cursor.fetchone()[0]
        
        # Check items in dictionary_items
        cursor.execute("SELECT COUNT(*) FROM dictionary_items")
        dict_items = cursor.fetchone()[0]
        
        # Check if approved items are in dictionary
        cursor.execute("""
            SELECT COUNT(DISTINCT a.item_label)
            FROM auto_learning_results a
            JOIN dictionary_items d ON (
                d.item_name = a.item_label AND 
                d.section_id = (SELECT id FROM dictionary_sections WHERE section_name = a.section_name)
            )
            WHERE a.auto_approved = 1 AND a.dictionary_updated = 1
        """)
        transferred_items = cursor.fetchone()[0]
        
        print(f"   📊 Approved items in auto_learning_results: {approved_items}")
        print(f"   📊 Total items in dictionary_items: {dict_items}")
        print(f"   📊 Approved items transferred to dictionary: {transferred_items}")
        
        if transferred_items > 0:
            print("   ✅ ISSUE 1 FIXED: Approved items are appearing in Dictionary Manager")
            issue1_fixed = True
        else:
            print("   ❌ ISSUE 1 NOT FIXED: Approved items not in Dictionary Manager")
            issue1_fixed = False
        
        # Show sample transferred items
        cursor.execute("""
            SELECT a.item_label, a.section_name, d.id as dict_id
            FROM auto_learning_results a
            JOIN dictionary_items d ON (
                d.item_name = a.item_label AND 
                d.section_id = (SELECT id FROM dictionary_sections WHERE section_name = a.section_name)
            )
            WHERE a.auto_approved = 1 AND a.dictionary_updated = 1
            LIMIT 5
        """)
        
        sample_items = cursor.fetchall()
        if sample_items:
            print("   📋 Sample transferred items:")
            for item_label, section_name, dict_id in sample_items:
                print(f"      {item_label} → {section_name} (dict_id: {dict_id})")
        
        conn.close()
        
        # 2. VERIFY ISSUE 2: Reject All Button Functionality
        print("\n2. ✅ VERIFYING ISSUE 2: Reject All Button Functionality")
        
        # Check if IPC handlers exist in main.js
        main_js_path = os.path.join(os.path.dirname(__file__), 'main.js')
        
        if os.path.exists(main_js_path):
            with open(main_js_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for reject-all-pending handler
            if "ipcMain.handle('reject-all-pending'" in content:
                print("   ✅ reject-all-pending IPC handler found in main.js")
                reject_handler_exists = True
            else:
                print("   ❌ reject-all-pending IPC handler missing in main.js")
                reject_handler_exists = False
            
            # Check for approve-all-pending handler
            if "ipcMain.handle('approve-all-pending'" in content:
                print("   ✅ approve-all-pending IPC handler found in main.js")
                approve_handler_exists = True
            else:
                print("   ❌ approve-all-pending IPC handler missing in main.js")
                approve_handler_exists = False
        else:
            print("   ❌ main.js not found")
            reject_handler_exists = False
            approve_handler_exists = False
        
        # Check if bulk operations exist in phased_process_manager.py
        phased_manager_path = os.path.join(os.path.dirname(__file__), 'core', 'phased_process_manager.py')
        
        if os.path.exists(phased_manager_path):
            with open(phased_manager_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for bulk operations methods
            if "def approve_all_pending_items(self)" in content:
                print("   ✅ approve_all_pending_items method found in phased_process_manager.py")
                approve_method_exists = True
            else:
                print("   ❌ approve_all_pending_items method missing in phased_process_manager.py")
                approve_method_exists = False
            
            if "def reject_all_pending_items(self)" in content:
                print("   ✅ reject_all_pending_items method found in phased_process_manager.py")
                reject_method_exists = True
            else:
                print("   ❌ reject_all_pending_items method missing in phased_process_manager.py")
                reject_method_exists = False
            
            # Check for command line handlers
            if "'approve-all-pending'" in content and "'reject-all-pending'" in content:
                print("   ✅ Command line handlers for bulk operations found")
                cmd_handlers_exist = True
            else:
                print("   ❌ Command line handlers for bulk operations missing")
                cmd_handlers_exist = False
        else:
            print("   ❌ phased_process_manager.py not found")
            approve_method_exists = False
            reject_method_exists = False
            cmd_handlers_exist = False
        
        issue2_fixed = (reject_handler_exists and approve_handler_exists and 
                       approve_method_exists and reject_method_exists and cmd_handlers_exist)
        
        if issue2_fixed:
            print("   ✅ ISSUE 2 FIXED: Reject All button functionality implemented")
        else:
            print("   ❌ ISSUE 2 NOT FIXED: Missing components for Reject All functionality")
        
        # 3. TEST BULK OPERATIONS
        print("\n3. 🧪 TESTING BULK OPERATIONS:")
        
        # Test get-pending-items
        try:
            result = subprocess.run([
                'python', 'core/phased_process_manager.py', 'get-pending-items'
            ], capture_output=True, text=True, cwd=os.path.dirname(__file__))
            
            if result.returncode == 0:
                try:
                    data = json.loads(result.stdout)
                    if data.get('success'):
                        pending_count = len(data.get('data', []))
                        print(f"   ✅ get-pending-items working: {pending_count} items found")
                        get_pending_works = True
                    else:
                        print(f"   ⚠️ get-pending-items returned error: {data.get('error')}")
                        get_pending_works = False
                except json.JSONDecodeError:
                    print(f"   ❌ get-pending-items returned invalid JSON: {result.stdout}")
                    get_pending_works = False
            else:
                print(f"   ❌ get-pending-items failed: {result.stderr}")
                get_pending_works = False
        except Exception as e:
            print(f"   ❌ get-pending-items test failed: {e}")
            get_pending_works = False
        
        # Test approve-all-pending (dry run)
        try:
            result = subprocess.run([
                'python', 'core/phased_process_manager.py', 'approve-all-pending'
            ], capture_output=True, text=True, cwd=os.path.dirname(__file__))
            
            if result.returncode == 0:
                try:
                    data = json.loads(result.stdout)
                    if 'success' in data:
                        print(f"   ✅ approve-all-pending command working: {data}")
                        approve_all_works = True
                    else:
                        print(f"   ❌ approve-all-pending returned unexpected format: {data}")
                        approve_all_works = False
                except json.JSONDecodeError:
                    print(f"   ❌ approve-all-pending returned invalid JSON: {result.stdout}")
                    approve_all_works = False
            else:
                print(f"   ❌ approve-all-pending failed: {result.stderr}")
                approve_all_works = False
        except Exception as e:
            print(f"   ❌ approve-all-pending test failed: {e}")
            approve_all_works = False
        
        # Test reject-all-pending (dry run)
        try:
            result = subprocess.run([
                'python', 'core/phased_process_manager.py', 'reject-all-pending'
            ], capture_output=True, text=True, cwd=os.path.dirname(__file__))
            
            if result.returncode == 0:
                try:
                    data = json.loads(result.stdout)
                    if 'success' in data:
                        print(f"   ✅ reject-all-pending command working: {data}")
                        reject_all_works = True
                    else:
                        print(f"   ❌ reject-all-pending returned unexpected format: {data}")
                        reject_all_works = False
                except json.JSONDecodeError:
                    print(f"   ❌ reject-all-pending returned invalid JSON: {result.stdout}")
                    reject_all_works = False
            else:
                print(f"   ❌ reject-all-pending failed: {result.stderr}")
                reject_all_works = False
        except Exception as e:
            print(f"   ❌ reject-all-pending test failed: {e}")
            reject_all_works = False
        
        bulk_operations_work = get_pending_works and approve_all_works and reject_all_works
        
        # 4. FINAL VERIFICATION SUMMARY
        print("\n4. 📋 FINAL VERIFICATION SUMMARY:")
        
        print(f"   {'✅' if issue1_fixed else '❌'} Issue 1 - Dictionary Manager Integration: {'FIXED' if issue1_fixed else 'NOT FIXED'}")
        print(f"   {'✅' if issue2_fixed else '❌'} Issue 2 - Reject All Button Functionality: {'FIXED' if issue2_fixed else 'NOT FIXED'}")
        print(f"   {'✅' if bulk_operations_work else '❌'} Bulk Operations Testing: {'WORKING' if bulk_operations_work else 'NOT WORKING'}")
        
        all_fixed = issue1_fixed and issue2_fixed and bulk_operations_work
        
        if all_fixed:
            print("\n   🎉 ALL ISSUES RESOLVED!")
            print("      • Approved items now appear in Dictionary Manager")
            print("      • Section assignments are preserved during transfer")
            print("      • Reject All button functionality is implemented")
            print("      • Approve All button functionality is implemented")
            print("      • Bulk operations are working correctly")
            print("      • Complete workflow validation successful")
        else:
            print("\n   ⚠️ SOME ISSUES REMAIN:")
            if not issue1_fixed:
                print("      • Dictionary Manager integration needs attention")
            if not issue2_fixed:
                print("      • Reject All button functionality needs completion")
            if not bulk_operations_work:
                print("      • Bulk operations need debugging")
        
        return all_fixed
        
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = verify_auto_learning_fixes()
    print(f"\n{'🎉 SUCCESS' if success else '❌ FAILED'}: Auto-Learning Workflow Verification")
    sys.exit(0 if success else 1)
