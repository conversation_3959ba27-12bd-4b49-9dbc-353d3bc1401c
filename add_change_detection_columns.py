#!/usr/bin/env python3
"""
CRITICAL DATABASE MIGRATION: Add Change Detection Columns
Adds the missing change detection columns to the dictionary_items table
"""

import sqlite3
import os
import sys
from datetime import datetime

def get_database_path():
    """Get the correct database path that the application uses"""
    # The application uses this specific database
    app_db_path = 'data/templar_payroll_auditor.db'
    
    if os.path.exists(app_db_path):
        return app_db_path
    
    # Fallback paths
    possible_paths = [
        'payroll_audit.db',
        'data/payroll_audit.db',
        'templar_payroll_auditor.db'
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    
    return app_db_path  # Default to app path

def check_existing_columns(cursor):
    """Check which change detection columns already exist"""
    cursor.execute("PRAGMA table_info(dictionary_items)")
    existing_columns = [row[1] for row in cursor.fetchall()]
    
    required_columns = [
        'include_new',
        'include_increase', 
        'include_decrease',
        'include_removed',
        'include_no_change'
    ]
    
    missing_columns = [col for col in required_columns if col not in existing_columns]
    existing_change_columns = [col for col in required_columns if col in existing_columns]
    
    return existing_columns, missing_columns, existing_change_columns

def add_missing_columns(cursor, conn, missing_columns):
    """Add missing change detection columns to dictionary_items table"""
    
    column_definitions = {
        'include_new': 'BOOLEAN DEFAULT 1',
        'include_increase': 'BOOLEAN DEFAULT 1', 
        'include_decrease': 'BOOLEAN DEFAULT 1',
        'include_removed': 'BOOLEAN DEFAULT 1',
        'include_no_change': 'BOOLEAN DEFAULT 0'
    }
    
    for column in missing_columns:
        try:
            sql = f"ALTER TABLE dictionary_items ADD COLUMN {column} {column_definitions[column]}"
            print(f"   Adding column: {column}")
            cursor.execute(sql)
            print(f"   ✅ Successfully added column: {column}")
        except Exception as e:
            print(f"   ❌ Error adding column {column}: {e}")
            return False
    
    conn.commit()
    return True

def verify_columns(cursor):
    """Verify that all change detection columns exist and have correct defaults"""
    cursor.execute("PRAGMA table_info(dictionary_items)")
    columns = cursor.fetchall()
    
    required_columns = [
        'include_new',
        'include_increase', 
        'include_decrease',
        'include_removed',
        'include_no_change'
    ]
    
    existing_columns = [row[1] for row in columns]
    
    print("   📊 Final column verification:")
    for col in required_columns:
        if col in existing_columns:
            print(f"      ✅ {col}")
        else:
            print(f"      ❌ {col} - MISSING")
    
    # Check if all required columns exist
    all_exist = all(col in existing_columns for col in required_columns)
    
    if all_exist:
        print("   ✅ All change detection columns verified")
        return True
    else:
        print("   ❌ Some change detection columns are still missing")
        return False

def update_existing_items_with_defaults(cursor, conn):
    """Update existing dictionary items with default change detection values"""
    try:
        # Count existing items
        cursor.execute("SELECT COUNT(*) FROM dictionary_items")
        item_count = cursor.fetchone()[0]
        
        if item_count > 0:
            print(f"   📊 Updating {item_count} existing items with default values...")
            
            # Update existing items with default values
            cursor.execute("""
                UPDATE dictionary_items 
                SET include_new = COALESCE(include_new, 1),
                    include_increase = COALESCE(include_increase, 1),
                    include_decrease = COALESCE(include_decrease, 1),
                    include_removed = COALESCE(include_removed, 1),
                    include_no_change = COALESCE(include_no_change, 0)
                WHERE include_new IS NULL 
                   OR include_increase IS NULL 
                   OR include_decrease IS NULL 
                   OR include_removed IS NULL 
                   OR include_no_change IS NULL
            """)
            
            updated_count = cursor.rowcount
            conn.commit()
            
            print(f"   ✅ Updated {updated_count} items with default change detection values")
        else:
            print("   ℹ️ No existing items to update")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error updating existing items: {e}")
        return False

def test_change_detection_functionality(cursor, conn):
    """Test that change detection columns work correctly"""
    try:
        print("   🧪 Testing change detection functionality...")
        
        # Insert a test item with specific change detection values
        test_section = 'MIGRATION_TEST_SECTION'
        test_item = 'MIGRATION_TEST_ITEM'
        
        # Clean up any existing test data
        cursor.execute("DELETE FROM dictionary_items WHERE item_name = ?", (test_item,))
        cursor.execute("DELETE FROM dictionary_sections WHERE section_name = ?", (test_section,))
        
        # Insert test section
        cursor.execute("INSERT INTO dictionary_sections (section_name) VALUES (?)", (test_section,))
        cursor.execute("SELECT id FROM dictionary_sections WHERE section_name = ?", (test_section,))
        section_id = cursor.fetchone()[0]
        
        # Insert test item with specific change detection values
        cursor.execute("""
            INSERT INTO dictionary_items
            (section_id, item_name, standard_key, format_type, value_format,
             include_in_report, include_new, include_increase, include_decrease,
             include_removed, include_no_change, is_fixed, validation_rules)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            section_id, test_item, test_item, 'text', 'text', 1,
            0, 1, 0, 1, 1, 0, '{}'  # Mixed values for testing
        ))
        
        conn.commit()
        
        # Verify the test item was saved correctly
        cursor.execute("""
            SELECT include_new, include_increase, include_decrease, include_removed, include_no_change
            FROM dictionary_items WHERE item_name = ?
        """, (test_item,))
        
        result = cursor.fetchone()
        expected = (0, 1, 0, 1, 1)
        
        if result == expected:
            print("   ✅ Change detection functionality test passed")
            success = True
        else:
            print(f"   ❌ Test failed. Expected: {expected}, Got: {result}")
            success = False
        
        # Clean up test data
        cursor.execute("DELETE FROM dictionary_items WHERE item_name = ?", (test_item,))
        cursor.execute("DELETE FROM dictionary_sections WHERE section_name = ?", (test_section,))
        conn.commit()
        
        return success
        
    except Exception as e:
        print(f"   ❌ Change detection test failed: {e}")
        return False

def main():
    """Main migration function"""
    print("🚀 CHANGE DETECTION COLUMNS MIGRATION")
    print("=" * 50)
    
    # Get database path
    db_path = get_database_path()
    print(f"📁 Target database: {db_path}")
    
    if not os.path.exists(db_path):
        print(f"❌ Database not found at: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\n1. 📊 CHECKING EXISTING COLUMNS:")
        existing_columns, missing_columns, existing_change_columns = check_existing_columns(cursor)
        
        print(f"   Total columns: {len(existing_columns)}")
        print(f"   Existing change detection columns: {len(existing_change_columns)}")
        print(f"   Missing change detection columns: {len(missing_columns)}")
        
        if existing_change_columns:
            print(f"   ✅ Found: {', '.join(existing_change_columns)}")
        
        if missing_columns:
            print(f"   ❌ Missing: {', '.join(missing_columns)}")
            
            print("\n2. 🔧 ADDING MISSING COLUMNS:")
            if add_missing_columns(cursor, conn, missing_columns):
                print("   ✅ All missing columns added successfully")
            else:
                print("   ❌ Failed to add some columns")
                return False
        else:
            print("   ✅ All change detection columns already exist")
        
        print("\n3. ✅ VERIFYING COLUMNS:")
        if not verify_columns(cursor):
            print("   ❌ Column verification failed")
            return False
        
        print("\n4. 🔄 UPDATING EXISTING ITEMS:")
        if not update_existing_items_with_defaults(cursor, conn):
            print("   ❌ Failed to update existing items")
            return False
        
        print("\n5. 🧪 TESTING FUNCTIONALITY:")
        if not test_change_detection_functionality(cursor, conn):
            print("   ❌ Functionality test failed")
            return False
        
        conn.close()
        
        print("\n" + "=" * 50)
        print("✅ CHANGE DETECTION COLUMNS MIGRATION COMPLETED!")
        print("🎯 Dictionary Manager save operations should now work correctly")
        print("🔧 All change detection toggles will persist properly")
        
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
