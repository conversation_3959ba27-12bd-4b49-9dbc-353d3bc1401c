import sqlite3
import os

print("Starting migration test...")

db_path = 'payroll_audit.db'
print(f"Database path: {db_path}")
print(f"Database exists: {os.path.exists(db_path)}")

try:
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    print("Connected to database")
    
    # Check tables
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = [row[0] for row in cursor.fetchall()]
    print(f"Existing tables: {tables}")
    
    # Create dictionary_sections if needed
    if 'dictionary_sections' not in tables:
        print("Creating dictionary_sections...")
        cursor.execute('''
            CREATE TABLE dictionary_sections (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                section_name TEXT UNIQUE NOT NULL,
                display_order INTEGER DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        conn.commit()
        print("Created dictionary_sections")
    
    # Create dictionary_items if needed
    if 'dictionary_items' not in tables:
        print("Creating dictionary_items with change detection columns...")
        cursor.execute('''
            CREATE TABLE dictionary_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                section_id INTEGER,
                item_name TEXT NOT NULL,
                standard_key TEXT,
                format_type TEXT,
                value_format TEXT,
                include_in_report BOOLEAN DEFAULT 1,
                include_new BOOLEAN DEFAULT 1,
                include_increase BOOLEAN DEFAULT 1,
                include_decrease BOOLEAN DEFAULT 1,
                include_removed BOOLEAN DEFAULT 1,
                include_no_change BOOLEAN DEFAULT 0,
                is_fixed BOOLEAN DEFAULT 0,
                validation_rules TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (section_id) REFERENCES dictionary_sections(id),
                UNIQUE(section_id, item_name)
            )
        ''')
        conn.commit()
        print("Created dictionary_items with change detection columns")
    
    conn.close()
    print("Migration completed successfully!")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
