#!/usr/bin/env python3
"""
Comprehensive "INCLUDE IN REPORT" Toggle Feature Audit
Production-ready audit of the complete toggle functionality
"""

import sys
import os
import sqlite3
import json

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def comprehensive_include_in_report_audit():
    """Comprehensive audit of the INCLUDE IN REPORT toggle functionality"""
    print("🔍 COMPREHENSIVE 'INCLUDE IN REPORT' TOGGLE AUDIT")
    print("=" * 70)
    
    audit_results = {
        'database_structure': False,
        'dictionary_integration': False,
        'ui_toggle_functionality': False,
        'report_filtering': False,
        'api_consistency': False,
        'edge_case_handling': False,
        'performance_optimization': False
    }
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return audit_results
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Database Structure Audit
        print("\n1. 📊 DATABASE STRUCTURE AUDIT:")
        
        try:
            # Check dictionary_items table structure
            cursor.execute("PRAGMA table_info(dictionary_items)")
            columns = cursor.fetchall()
            
            include_column_exists = any(col[1] == 'include_in_report' for col in columns)
            print(f"   include_in_report column exists: {'✅' if include_column_exists else '❌'}")
            
            if include_column_exists:
                # Check data distribution
                cursor.execute("SELECT include_in_report, COUNT(*) FROM dictionary_items GROUP BY include_in_report")
                distribution = cursor.fetchall()
                
                print("   Data distribution:")
                for value, count in distribution:
                    status = "INCLUDED" if value else "EXCLUDED"
                    print(f"     {status}: {count} items")
                
                audit_results['database_structure'] = True
            else:
                print("   ❌ include_in_report column missing from dictionary_items table")
                
        except Exception as e:
            print(f"   ❌ Database structure audit failed: {e}")
        
        # 2. Dictionary Integration Audit
        print("\n2. 📚 DICTIONARY INTEGRATION AUDIT:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.dictionary_manager import PayrollDictionaryManager
            
            manager = PayrollDictionaryManager(debug=False)
            
            # Test should_include_in_report method
            test_items = [
                ("PERSONAL DETAILS", "EMPLOYEE NO."),
                ("EARNINGS", "BASIC SALARY"),
                ("DEDUCTIONS", "INCOME TAX"),
                ("LOANS", "SALARY ADVANCE-MINS")
            ]
            
            print("   Testing should_include_in_report method:")
            all_tests_passed = True
            
            for section, item in test_items:
                try:
                    result = manager.should_include_in_report(section, item)
                    print(f"     {section}.{item}: {'✅ INCLUDE' if result else '❌ EXCLUDE'}")
                except Exception as e:
                    print(f"     {section}.{item}: ❌ ERROR - {e}")
                    all_tests_passed = False
            
            if all_tests_passed:
                audit_results['dictionary_integration'] = True
                print("   ✅ Dictionary integration working correctly")
            else:
                print("   ❌ Dictionary integration has issues")
                
        except Exception as e:
            print(f"   ❌ Dictionary integration audit failed: {e}")
        
        # 3. UI Toggle Functionality Audit
        print("\n3. 🖥️ UI TOGGLE FUNCTIONALITY AUDIT:")
        
        try:
            # Check if UI files contain toggle implementation
            ui_files_to_check = [
                'renderer.js',
                'styles.css',
                'index.html'
            ]
            
            toggle_implementations = 0
            
            for file_name in ui_files_to_check:
                if os.path.exists(file_name):
                    with open(file_name, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                    if 'include-toggle' in content or 'include_in_report' in content:
                        toggle_implementations += 1
                        print(f"     ✅ {file_name}: Toggle implementation found")
                    else:
                        print(f"     ⚠️ {file_name}: No toggle implementation found")
                else:
                    print(f"     ❌ {file_name}: File not found")
            
            if toggle_implementations >= 2:  # At least JS and CSS
                audit_results['ui_toggle_functionality'] = True
                print("   ✅ UI toggle functionality implemented")
            else:
                print("   ❌ UI toggle functionality incomplete")
                
        except Exception as e:
            print(f"   ❌ UI toggle functionality audit failed: {e}")
        
        # 4. Report Filtering Audit
        print("\n4. 📄 REPORT FILTERING AUDIT:")
        
        try:
            # Check if report generation uses include_in_report filtering
            report_files_to_check = [
                'core/advanced_reporting_system.py',
                'core/phased_process_manager.py',
                'renderer.js'
            ]
            
            filtering_implementations = 0
            
            for file_name in report_files_to_check:
                if os.path.exists(file_name):
                    with open(file_name, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                    if 'include_in_report' in content or 'includeInReport' in content:
                        filtering_implementations += 1
                        print(f"     ✅ {file_name}: Report filtering found")
                    else:
                        print(f"     ⚠️ {file_name}: No report filtering found")
                else:
                    print(f"     ❌ {file_name}: File not found")
            
            if filtering_implementations >= 1:
                audit_results['report_filtering'] = True
                print("   ✅ Report filtering implemented")
            else:
                print("   ❌ Report filtering not implemented")
                
        except Exception as e:
            print(f"   ❌ Report filtering audit failed: {e}")
        
        # 5. API Consistency Audit
        print("\n5. 🔄 API CONSISTENCY AUDIT:")
        
        try:
            # Test dictionary integration API
            from core.dictionary_integration import should_include_in_report
            
            # Test consistency between different APIs
            test_cases = [
                ("EARNINGS", "BASIC SALARY"),
                ("DEDUCTIONS", "INCOME TAX")
            ]
            
            api_consistent = True
            
            for section, item in test_cases:
                try:
                    result1 = manager.should_include_in_report(section, item)
                    result2 = should_include_in_report(section, item)
                    
                    if result1 == result2:
                        print(f"     ✅ {section}.{item}: APIs consistent ({result1})")
                    else:
                        print(f"     ❌ {section}.{item}: APIs inconsistent ({result1} vs {result2})")
                        api_consistent = False
                        
                except Exception as e:
                    print(f"     ❌ {section}.{item}: API test failed - {e}")
                    api_consistent = False
            
            if api_consistent:
                audit_results['api_consistency'] = True
                print("   ✅ API consistency verified")
            else:
                print("   ❌ API consistency issues found")
                
        except Exception as e:
            print(f"   ❌ API consistency audit failed: {e}")
        
        # 6. Edge Case Handling Audit
        print("\n6. 🛡️ EDGE CASE HANDLING AUDIT:")
        
        try:
            edge_cases_passed = 0
            total_edge_cases = 4
            
            # Test 1: Non-existent section
            try:
                result = manager.should_include_in_report("NON_EXISTENT_SECTION", "ITEM")
                if result == True:  # Should default to True
                    print("     ✅ Non-existent section: Defaults to include")
                    edge_cases_passed += 1
                else:
                    print("     ❌ Non-existent section: Incorrect default")
            except:
                print("     ❌ Non-existent section: Exception thrown")
            
            # Test 2: Non-existent item
            try:
                result = manager.should_include_in_report("EARNINGS", "NON_EXISTENT_ITEM")
                if result == True:  # Should default to True
                    print("     ✅ Non-existent item: Defaults to include")
                    edge_cases_passed += 1
                else:
                    print("     ❌ Non-existent item: Incorrect default")
            except:
                print("     ❌ Non-existent item: Exception thrown")
            
            # Test 3: Empty/None values
            try:
                result1 = manager.should_include_in_report("", "ITEM")
                result2 = manager.should_include_in_report("SECTION", "")
                if result1 == True and result2 == True:
                    print("     ✅ Empty values: Handled correctly")
                    edge_cases_passed += 1
                else:
                    print("     ❌ Empty values: Not handled correctly")
            except:
                print("     ❌ Empty values: Exception thrown")
            
            # Test 4: Case sensitivity
            try:
                result1 = manager.should_include_in_report("EARNINGS", "BASIC SALARY")
                result2 = manager.should_include_in_report("earnings", "basic salary")
                # Results may differ due to case sensitivity - this is expected
                print(f"     ✅ Case sensitivity: Handled (upper: {result1}, lower: {result2})")
                edge_cases_passed += 1
            except:
                print("     ❌ Case sensitivity: Exception thrown")
            
            if edge_cases_passed >= total_edge_cases * 0.75:  # 75% pass rate
                audit_results['edge_case_handling'] = True
                print(f"   ✅ Edge case handling: {edge_cases_passed}/{total_edge_cases} passed")
            else:
                print(f"   ❌ Edge case handling: {edge_cases_passed}/{total_edge_cases} passed")
                
        except Exception as e:
            print(f"   ❌ Edge case handling audit failed: {e}")
        
        # 7. Performance Optimization Audit
        print("\n7. ⚡ PERFORMANCE OPTIMIZATION AUDIT:")
        
        try:
            import time
            
            # Test performance of include_in_report checks
            start_time = time.time()
            
            for i in range(100):
                manager.should_include_in_report("EARNINGS", "BASIC SALARY")
            
            end_time = time.time()
            avg_time = (end_time - start_time) / 100
            
            print(f"     Average check time: {avg_time:.6f} seconds")
            
            if avg_time < 0.001:  # Less than 1ms per check
                audit_results['performance_optimization'] = True
                print("     ✅ Performance: Excellent (< 1ms per check)")
            elif avg_time < 0.01:  # Less than 10ms per check
                audit_results['performance_optimization'] = True
                print("     ✅ Performance: Good (< 10ms per check)")
            else:
                print("     ⚠️ Performance: Could be improved (> 10ms per check)")
                
        except Exception as e:
            print(f"   ❌ Performance optimization audit failed: {e}")
        
        # 8. Final Audit Summary
        print("\n8. 📋 AUDIT SUMMARY:")
        print("   " + "=" * 50)
        
        passed_audits = sum(audit_results.values())
        total_audits = len(audit_results)
        
        for audit_name, result in audit_results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"   {audit_name.replace('_', ' ').title()}: {status}")
        
        print("   " + "=" * 50)
        print(f"   OVERALL RESULT: {passed_audits}/{total_audits} audits passed")
        
        if passed_audits == total_audits:
            print("   🎯 INCLUDE IN REPORT TOGGLE: FULLY AUDITED ✅")
            print("   🚀 PRODUCTION READY: All functionality verified")
        elif passed_audits >= total_audits * 0.8:
            print("   ⚠️ INCLUDE IN REPORT TOGGLE: MOSTLY VERIFIED")
            print("   🔧 MINOR ISSUES: Some components need attention")
        else:
            print("   ❌ INCLUDE IN REPORT TOGGLE: AUDIT FAILED")
            print("   🚨 CRITICAL ISSUES: Feature needs major fixes")
        
        conn.close()
        return audit_results
        
    except Exception as e:
        print(f"❌ Critical error during audit: {e}")
        import traceback
        traceback.print_exc()
        return audit_results

if __name__ == "__main__":
    results = comprehensive_include_in_report_audit()
    
    # Exit with appropriate code
    passed_audits = sum(results.values())
    total_audits = len(results)
    
    if passed_audits == total_audits:
        sys.exit(0)  # All audits passed
    else:
        sys.exit(1)  # Some audits failed
