#!/usr/bin/env python3
"""
Auto-Learning System Investigation
Comprehensive analysis of why no items are appearing in the Auto-Learning system
"""

import sys
import os
import sqlite3
import json
from datetime import datetime, timed<PERSON><PERSON>

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def investigate_auto_learning_system():
    """Comprehensive investigation of Auto-Learning system issues"""
    print("🔍 AUTO-LEARNING SYSTEM INVESTIGATION")
    print("=" * 60)
    
    investigation_results = {
        'database_connectivity': False,
        'auto_learning_tables_exist': False,
        'session_data_available': False,
        'comparison_results_exist': False,
        'auto_learning_data_capture': False,
        'ui_backend_connectivity': False,
        'data_flow_integrity': False
    }
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return investigation_results
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        investigation_results['database_connectivity'] = True
        print("✅ Database connectivity established")
        
        # 1. Check Auto-Learning Tables
        print("\n1. 📊 AUTO-LEARNING TABLES VERIFICATION:")
        
        # Check if auto_learning_items table exists
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='auto_learning_items'
        """)
        
        auto_learning_table = cursor.fetchone()
        
        if auto_learning_table:
            print("   ✅ auto_learning_items table exists")
            
            # Check table structure
            cursor.execute("PRAGMA table_info(auto_learning_items)")
            columns = cursor.fetchall()
            
            print("   📋 Table structure:")
            for col in columns:
                print(f"     {col[1]} ({col[2]})")
            
            # Check if table has any data
            cursor.execute("SELECT COUNT(*) FROM auto_learning_items")
            total_items = cursor.fetchone()[0]
            print(f"   📊 Total items in auto_learning_items: {total_items}")
            
            investigation_results['auto_learning_tables_exist'] = True
        else:
            print("   ❌ auto_learning_items table does not exist")
        
        # 2. Check Session Data
        print("\n2. 🎯 SESSION DATA VERIFICATION:")
        
        # Get current session
        sys.path.append(os.path.dirname(__file__))
        try:
            from core.session_manager import get_current_session_id
            current_session = get_current_session_id()
            print(f"   📋 Current session ID: {current_session}")
            
            if current_session:
                investigation_results['session_data_available'] = True
            
        except Exception as e:
            print(f"   ❌ Session manager error: {e}")
            # Try to get session from database directly
            cursor.execute("""
                SELECT DISTINCT session_id FROM comparison_results 
                ORDER BY session_id DESC LIMIT 1
            """)
            session_result = cursor.fetchone()
            if session_result:
                current_session = session_result[0]
                print(f"   📋 Latest session from database: {current_session}")
                investigation_results['session_data_available'] = True
            else:
                print("   ❌ No session data found")
                current_session = None
        
        # 3. Check Comparison Results
        print("\n3. ⚖️ COMPARISON RESULTS VERIFICATION:")
        
        if current_session:
            cursor.execute("""
                SELECT COUNT(*) FROM comparison_results 
                WHERE session_id = ?
            """, (current_session,))
            
            comparison_count = cursor.fetchone()[0]
            print(f"   📊 Comparison results for session {current_session}: {comparison_count}")
            
            if comparison_count > 0:
                investigation_results['comparison_results_exist'] = True
                
                # Check recent comparison results
                cursor.execute("""
                    SELECT section_name, item_label, change_type, COUNT(*) as count
                    FROM comparison_results 
                    WHERE session_id = ?
                    GROUP BY section_name, item_label, change_type
                    ORDER BY count DESC
                    LIMIT 10
                """, (current_session,))
                
                recent_results = cursor.fetchall()
                print("   📋 Recent comparison results (top 10):")
                for section, item, change_type, count in recent_results:
                    print(f"     {section}.{item} ({change_type}): {count} items")
            else:
                print("   ❌ No comparison results found for current session")
        else:
            print("   ❌ Cannot check comparison results - no session available")
        
        # 4. Check Auto-Learning Data Capture
        print("\n4. 🤖 AUTO-LEARNING DATA CAPTURE VERIFICATION:")
        
        if auto_learning_table and current_session:
            # Check if any items were captured for current session
            cursor.execute("""
                SELECT COUNT(*) FROM auto_learning_items 
                WHERE session_id = ?
            """, (current_session,))
            
            auto_learning_count = cursor.fetchone()[0]
            print(f"   📊 Auto-learning items for session {current_session}: {auto_learning_count}")
            
            if auto_learning_count > 0:
                investigation_results['auto_learning_data_capture'] = True
                
                # Show recent auto-learning items
                cursor.execute("""
                    SELECT item_name, suggested_section, confidence_score, status, created_at
                    FROM auto_learning_items 
                    WHERE session_id = ?
                    ORDER BY created_at DESC
                    LIMIT 5
                """, (current_session,))
                
                auto_items = cursor.fetchall()
                print("   📋 Recent auto-learning items:")
                for item_name, section, confidence, status, created_at in auto_items:
                    print(f"     {item_name} → {section} (confidence: {confidence}, status: {status})")
            else:
                print("   ❌ No auto-learning items captured for current session")
                
                # Check if there are any auto-learning items at all
                cursor.execute("SELECT COUNT(*) FROM auto_learning_items")
                total_auto_items = cursor.fetchone()[0]
                print(f"   📊 Total auto-learning items (all sessions): {total_auto_items}")
                
                if total_auto_items > 0:
                    # Show items from other sessions
                    cursor.execute("""
                        SELECT session_id, COUNT(*) as count
                        FROM auto_learning_items 
                        GROUP BY session_id
                        ORDER BY session_id DESC
                        LIMIT 5
                    """)
                    
                    session_counts = cursor.fetchall()
                    print("   📋 Auto-learning items by session:")
                    for session_id, count in session_counts:
                        print(f"     Session {session_id}: {count} items")
        
        # 5. Test Auto-Learning System API
        print("\n5. 🔄 AUTO-LEARNING SYSTEM API VERIFICATION:")
        
        try:
            from core.auto_learning_system import AutoLearningSystem
            
            auto_learning = AutoLearningSystem()
            print("   ✅ Auto-Learning system imported successfully")
            
            # Test get_pending_items method
            pending_items = auto_learning.get_pending_auto_learning_items()
            print(f"   📊 Pending items from API: {len(pending_items)}")
            
            if len(pending_items) > 0:
                investigation_results['ui_backend_connectivity'] = True
                print("   📋 Sample pending items:")
                for i, item in enumerate(pending_items[:3]):
                    print(f"     {i+1}. {item.get('item_name')} → {item.get('suggested_section')}")
            else:
                print("   ❌ No pending items returned from API")
                
                # Test if the method is filtering by session correctly
                if current_session:
                    # Check if method is using correct session
                    print(f"   🔍 Testing with session {current_session}")
                    
                    # Try to get items without session filtering
                    cursor.execute("""
                        SELECT item_name, suggested_section, confidence_score, status
                        FROM auto_learning_items 
                        WHERE status = 'pending'
                        LIMIT 5
                    """)
                    
                    all_pending = cursor.fetchall()
                    print(f"   📊 All pending items in database: {len(all_pending)}")
                    
                    if all_pending:
                        print("   📋 Pending items in database:")
                        for item_name, section, confidence, status in all_pending:
                            print(f"     {item_name} → {section} (confidence: {confidence})")
                        
                        print("   ⚠️ Items exist in database but API is not returning them")
                        print("   🔍 Possible session filtering issue")
            
        except Exception as e:
            print(f"   ❌ Auto-Learning system API error: {e}")
            import traceback
            traceback.print_exc()
        
        # 6. Check Data Flow Integrity
        print("\n6. 🔄 DATA FLOW INTEGRITY CHECK:")
        
        if investigation_results['comparison_results_exist']:
            # Check if comparison results should have triggered auto-learning
            cursor.execute("""
                SELECT DISTINCT item_label
                FROM comparison_results 
                WHERE session_id = ? AND change_type = 'NEW'
                LIMIT 10
            """, (current_session,))
            
            new_items = cursor.fetchall()
            print(f"   📊 NEW items in comparison results: {len(new_items)}")
            
            if new_items:
                print("   📋 Sample NEW items that should trigger auto-learning:")
                for item_name, in new_items[:5]:
                    print(f"     {item_name}")
                
                # Check if these items are in auto-learning
                new_item_names = [item[0] for item in new_items]
                placeholders = ','.join(['?' for _ in new_item_names])
                
                cursor.execute(f"""
                    SELECT item_name FROM auto_learning_items 
                    WHERE item_name IN ({placeholders}) AND session_id = ?
                """, new_item_names + [current_session])
                
                captured_items = cursor.fetchall()
                print(f"   📊 NEW items captured in auto-learning: {len(captured_items)}")
                
                if len(captured_items) == 0:
                    print("   ❌ NEW items not being captured by auto-learning system")
                    print("   🔍 Data flow broken between comparison results and auto-learning")
                else:
                    investigation_results['data_flow_integrity'] = True
                    print("   ✅ Data flow integrity confirmed")
            else:
                print("   ⚠️ No NEW items found in comparison results")
                print("   💡 Auto-learning typically processes NEW items")
        
        # 7. Check Recent Processing Activity
        print("\n7. ⏰ RECENT PROCESSING ACTIVITY:")
        
        # Check when the last comparison results were created
        cursor.execute("""
            SELECT MAX(created_at) FROM comparison_results 
            WHERE session_id = ?
        """, (current_session,))
        
        last_comparison = cursor.fetchone()[0]
        
        if last_comparison:
            print(f"   📅 Last comparison results: {last_comparison}")
            
            # Check if auto-learning was triggered after comparison
            cursor.execute("""
                SELECT MAX(created_at) FROM auto_learning_items 
                WHERE session_id = ?
            """, (current_session,))
            
            last_auto_learning = cursor.fetchone()[0]
            
            if last_auto_learning:
                print(f"   📅 Last auto-learning activity: {last_auto_learning}")
                
                # Compare timestamps
                if last_auto_learning >= last_comparison:
                    print("   ✅ Auto-learning activity after comparison results")
                else:
                    print("   ❌ Auto-learning activity before comparison results")
                    print("   🔍 Auto-learning may not be triggered by recent processing")
            else:
                print("   ❌ No auto-learning activity found")
                print("   🔍 Auto-learning system may not be triggered during processing")
        else:
            print("   ❌ No comparison results timestamp found")
        
        conn.close()
        
        # Summary
        print("\n8. 📋 INVESTIGATION SUMMARY:")
        print("   " + "=" * 50)
        
        passed_checks = sum(investigation_results.values())
        total_checks = len(investigation_results)
        
        for check_name, result in investigation_results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"   {check_name.replace('_', ' ').title()}: {status}")
        
        print("   " + "=" * 50)
        print(f"   OVERALL RESULT: {passed_checks}/{total_checks} checks passed")
        
        # Identify likely issues
        print("\n9. 🎯 LIKELY ISSUES IDENTIFIED:")
        
        if not investigation_results['auto_learning_tables_exist']:
            print("   🚨 CRITICAL: Auto-learning tables missing")
        
        if not investigation_results['session_data_available']:
            print("   🚨 CRITICAL: Session data not available")
        
        if not investigation_results['comparison_results_exist']:
            print("   🚨 CRITICAL: No comparison results for current session")
        
        if not investigation_results['auto_learning_data_capture']:
            print("   ⚠️ WARNING: Auto-learning not capturing data")
        
        if not investigation_results['ui_backend_connectivity']:
            print("   ⚠️ WARNING: UI not receiving data from backend")
        
        if not investigation_results['data_flow_integrity']:
            print("   ⚠️ WARNING: Data flow broken between processing and auto-learning")
        
        if passed_checks == total_checks:
            print("   ✅ All systems operational - investigate UI refresh or timing issues")
        elif passed_checks < total_checks // 2:
            print("   🚨 CRITICAL ISSUES: Multiple system failures detected")
        else:
            print("   ⚠️ MODERATE ISSUES: Some components need attention")
        
        return investigation_results
        
    except Exception as e:
        print(f"❌ Critical error during investigation: {e}")
        import traceback
        traceback.print_exc()
        return investigation_results

if __name__ == "__main__":
    results = investigate_auto_learning_system()
    
    # Exit with appropriate code
    passed_checks = sum(results.values())
    total_checks = len(results)
    
    if passed_checks >= total_checks * 0.8:  # 80% pass threshold
        sys.exit(0)  # Most systems working
    else:
        sys.exit(1)  # Critical issues found
