#!/usr/bin/env python3
"""
Debug Pre-Reporting Command Issues
"""

import sys
import os
import json
import subprocess

def debug_pre_reporting_command():
    """Debug the pre-reporting command issues"""
    print("🔍 DEBUGGING PRE-REPORTING COMMAND")
    print("=" * 50)
    
    try:
        # 1. Test the method directly with debug output
        print("1. 🧪 TESTING METHOD DIRECTLY WITH DEBUG:")
        
        sys.path.append(os.path.dirname(__file__))
        from core.phased_process_manager import PhasedProcessManager
        
        manager = PhasedProcessManager(debug_mode=True)
        result = manager.get_pre_reporting_data()
        
        print(f"   📊 Direct result: {result}")
        print(f"   📊 Result type: {type(result)}")
        
        if isinstance(result, dict):
            print(f"   📊 Success: {result.get('success')}")
            print(f"   📊 Error: {result.get('error')}")
            print(f"   📊 Data count: {len(result.get('data', []))}")
        
        # 2. Test command line with enhanced error handling
        print("\n2. 🧪 TESTING COMMAND LINE WITH ENHANCED ERROR HANDLING:")
        
        # Create a test script that adds more debugging
        test_script = '''
import sys
import os
import json

# Add current directory to path
sys.path.append('.')

try:
    print("DEBUG: Starting import...", file=sys.stderr)
    from core.phased_process_manager import PhasedProcessManager
    print("DEBUG: Import successful", file=sys.stderr)
    
    print("DEBUG: Creating manager...", file=sys.stderr)
    manager = PhasedProcessManager(debug_mode=False)
    print("DEBUG: Manager created", file=sys.stderr)
    
    print("DEBUG: Calling get_pre_reporting_data...", file=sys.stderr)
    result = manager.get_pre_reporting_data()
    print(f"DEBUG: Result obtained: {type(result)}", file=sys.stderr)
    
    print("DEBUG: Converting to JSON...", file=sys.stderr)
    json_result = json.dumps(result)
    print("DEBUG: JSON conversion successful", file=sys.stderr)
    
    print(json_result)
    print("DEBUG: Output printed", file=sys.stderr)
    
except Exception as e:
    print(f"DEBUG: Exception occurred: {e}", file=sys.stderr)
    import traceback
    traceback.print_exc(file=sys.stderr)
    print(json.dumps({"success": False, "error": str(e)}))
'''
        
        with open('test_pre_reporting_debug.py', 'w') as f:
            f.write(test_script)
        
        # Run the test script
        result = subprocess.run([
            'python', 'test_pre_reporting_debug.py'
        ], capture_output=True, text=True, timeout=30)
        
        print(f"   📊 Return code: {result.returncode}")
        print(f"   📊 STDOUT: '{result.stdout}'")
        print(f"   📊 STDERR: '{result.stderr}'")
        
        # 3. Test the actual command that Electron uses
        print("\n3. 🧪 TESTING ACTUAL ELECTRON COMMAND:")
        
        result = subprocess.run([
            'python', 'core/phased_process_manager.py', 'get-latest-pre-reporting-data'
        ], capture_output=True, text=True, timeout=30)
        
        print(f"   📊 Return code: {result.returncode}")
        print(f"   📊 STDOUT: '{result.stdout}'")
        print(f"   📊 STDERR: '{result.stderr}'")
        
        # 4. Check if there are any import issues
        print("\n4. 🧪 TESTING IMPORTS:")
        
        try:
            from core.phased_process_manager import PhasedProcessManager
            print("   ✅ PhasedProcessManager import successful")
            
            manager = PhasedProcessManager(debug_mode=False)
            print("   ✅ Manager creation successful")
            
            # Check if the method exists
            if hasattr(manager, 'get_pre_reporting_data'):
                print("   ✅ get_pre_reporting_data method exists")
            else:
                print("   ❌ get_pre_reporting_data method missing")
            
        except Exception as e:
            print(f"   ❌ Import test failed: {e}")
        
        # 5. Check database connectivity in command line mode
        print("\n5. 🧪 TESTING DATABASE CONNECTIVITY:")
        
        try:
            import sqlite3
            
            db_paths = [
                r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
                r"data\templar_payroll_auditor.db",
                r"templar_payroll_auditor.db"
            ]
            
            db_path = None
            for path in db_paths:
                if os.path.exists(path):
                    db_path = path
                    break
            
            if db_path:
                print(f"   ✅ Database found: {db_path}")
                
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # Check comparison_results
                cursor.execute("SELECT COUNT(*) FROM comparison_results")
                comparison_count = cursor.fetchone()[0]
                print(f"   📊 Comparison results: {comparison_count}")
                
                conn.close()
            else:
                print("   ❌ Database not found")
        
        except Exception as e:
            print(f"   ❌ Database test failed: {e}")
        
        # 6. Create a minimal working version
        print("\n6. 🧪 CREATING MINIMAL WORKING VERSION:")
        
        minimal_script = '''
import sys
import os
import json
import sqlite3

try:
    # Simple database query
    db_path = r"C:\\THE PAYROLL AUDITOR\\data\\templar_payroll_auditor.db"
    if os.path.exists(db_path):
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM comparison_results")
        count = cursor.fetchone()[0]
        
        conn.close()
        
        result = {
            "success": True,
            "data": [],
            "total_changes": count,
            "session_id": "test"
        }
        
        print(json.dumps(result))
    else:
        print(json.dumps({"success": False, "error": "Database not found"}))

except Exception as e:
    print(json.dumps({"success": False, "error": str(e)}))
'''
        
        with open('minimal_pre_reporting_test.py', 'w') as f:
            f.write(minimal_script)
        
        result = subprocess.run([
            'python', 'minimal_pre_reporting_test.py'
        ], capture_output=True, text=True, timeout=30)
        
        print(f"   📊 Minimal test return code: {result.returncode}")
        print(f"   📊 Minimal test STDOUT: '{result.stdout}'")
        print(f"   📊 Minimal test STDERR: '{result.stderr}'")
        
        if result.stdout.strip():
            try:
                parsed = json.loads(result.stdout.strip())
                print(f"   ✅ Minimal test JSON: {parsed}")
            except json.JSONDecodeError as e:
                print(f"   ❌ Minimal test JSON error: {e}")
        
        # Clean up test files
        for test_file in ['test_pre_reporting_debug.py', 'minimal_pre_reporting_test.py']:
            if os.path.exists(test_file):
                os.remove(test_file)
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_pre_reporting_command()
