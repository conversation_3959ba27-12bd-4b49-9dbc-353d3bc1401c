#!/usr/bin/env python3
"""
Comprehensive Auto-Learning System Validation
Production-ready validation of the complete Auto-Learning data flow
"""

import sys
import os
import sqlite3
import json
import subprocess

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def comprehensive_auto_learning_validation():
    """Comprehensive validation of the Auto-Learning system"""
    print("🔍 COMPREHENSIVE AUTO-LEARNING SYSTEM VALIDATION")
    print("=" * 70)
    
    validation_results = {
        'data_storage': False,
        'api_access': False,
        'session_filtering': False,
        'ui_integration': False,
        'command_line': False,
        'error_handling': False
    }
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return validation_results
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Data Storage Validation
        print("\n1. 📊 DATA STORAGE VALIDATION:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.session_manager import get_current_session_id
            
            current_session = get_current_session_id()
            print(f"   Current session: {current_session}")
            
            # Check data exists
            cursor.execute("""
                SELECT COUNT(*) FROM auto_learning_results 
                WHERE session_id = ? AND auto_approved = 0 AND dictionary_updated = 0
            """, (current_session,))
            
            pending_count = cursor.fetchone()[0]
            print(f"   Pending items in database: {pending_count}")
            
            if pending_count > 0:
                validation_results['data_storage'] = True
                print("   ✅ Data storage validation: PASSED")
            else:
                print("   ❌ Data storage validation: FAILED - No pending items")
                
        except Exception as e:
            print(f"   ❌ Data storage validation: FAILED - {e}")
        
        # 2. API Access Validation
        print("\n2. 🔄 API ACCESS VALIDATION:")
        
        try:
            from core.phased_process_manager import PhasedProcessManager
            
            manager = PhasedProcessManager(debug_mode=False)  # Disable debug for clean output
            manager.session_id = current_session
            
            result = manager.get_pending_auto_learning_items()
            
            if result.get('success') and result.get('count', 0) > 0:
                validation_results['api_access'] = True
                print(f"   ✅ API access validation: PASSED - {result['count']} items accessible")
            else:
                print(f"   ❌ API access validation: FAILED - {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"   ❌ API access validation: FAILED - {e}")
        
        # 3. Session Filtering Validation
        print("\n3. 🎯 SESSION FILTERING VALIDATION:")
        
        try:
            # Test with correct session
            correct_session_result = manager.get_pending_auto_learning_items()
            correct_count = correct_session_result.get('count', 0)
            
            # Test with wrong session
            manager.session_id = 'wrong_session_id'
            wrong_session_result = manager.get_pending_auto_learning_items()
            wrong_count = wrong_session_result.get('count', 0)
            
            # Restore correct session
            manager.session_id = current_session
            
            print(f"   Correct session items: {correct_count}")
            print(f"   Wrong session items: {wrong_count}")
            
            if correct_count > 0 and wrong_count == 0:
                validation_results['session_filtering'] = True
                print("   ✅ Session filtering validation: PASSED")
            else:
                print("   ❌ Session filtering validation: FAILED")
                
        except Exception as e:
            print(f"   ❌ Session filtering validation: FAILED - {e}")
        
        # 4. Command Line Interface Validation
        print("\n4. 💻 COMMAND LINE INTERFACE VALIDATION:")
        
        try:
            result = subprocess.run([
                'python', 
                'core/phased_process_manager.py', 
                'get-pending-items'
            ], capture_output=True, text=True, cwd=os.path.dirname(__file__))
            
            if result.returncode == 0:
                output = result.stdout.strip()
                json_start = output.find('{')
                if json_start >= 0:
                    json_part = output[json_start:]
                    json_end = json_part.rfind('}') + 1
                    if json_end > 0:
                        clean_json = json_part[:json_end]
                        parsed_data = json.loads(clean_json)
                        
                        if parsed_data.get('success') and parsed_data.get('count', 0) > 0:
                            validation_results['command_line'] = True
                            print(f"   ✅ Command line validation: PASSED - {parsed_data['count']} items")
                        else:
                            print("   ❌ Command line validation: FAILED - No items returned")
                    else:
                        print("   ❌ Command line validation: FAILED - Invalid JSON format")
                else:
                    print("   ❌ Command line validation: FAILED - No JSON in output")
            else:
                print(f"   ❌ Command line validation: FAILED - {result.stderr}")
                
        except Exception as e:
            print(f"   ❌ Command line validation: FAILED - {e}")
        
        # 5. UI Integration Validation
        print("\n5. 🖥️ UI INTEGRATION VALIDATION:")
        
        try:
            # Test Enhanced Dictionary Auto-Learning (used by UI)
            from backup_created_files.enhanced_dictionary_auto_learning import EnhancedDictionaryAutoLearning
            
            auto_learning = EnhancedDictionaryAutoLearning(debug=False)
            all_pending = auto_learning.get_pending_items()
            session_pending = [item for item in all_pending if item.get('session_id') == current_session]
            
            if len(session_pending) > 0:
                validation_results['ui_integration'] = True
                print(f"   ✅ UI integration validation: PASSED - {len(session_pending)} items for UI")
            else:
                print("   ❌ UI integration validation: FAILED - No items for UI")
                
        except Exception as e:
            print(f"   ❌ UI integration validation: FAILED - {e}")
        
        # 6. Error Handling Validation
        print("\n6. 🛡️ ERROR HANDLING VALIDATION:")
        
        try:
            # Test with invalid session
            manager.session_id = None
            no_session_result = manager.get_pending_auto_learning_items()
            
            # Test with database error simulation
            original_db = manager.db_manager
            manager.db_manager = None
            no_db_result = manager.get_pending_auto_learning_items()
            
            # Restore
            manager.db_manager = original_db
            manager.session_id = current_session
            
            if (no_session_result.get('success') is not None and 
                no_db_result.get('success') is False):
                validation_results['error_handling'] = True
                print("   ✅ Error handling validation: PASSED")
            else:
                print("   ❌ Error handling validation: FAILED")
                
        except Exception as e:
            print(f"   ❌ Error handling validation: FAILED - {e}")
        
        # 7. Final Validation Summary
        print("\n7. 📋 VALIDATION SUMMARY:")
        print("   " + "=" * 50)
        
        passed_tests = sum(validation_results.values())
        total_tests = len(validation_results)
        
        for test_name, result in validation_results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"   {test_name.replace('_', ' ').title()}: {status}")
        
        print("   " + "=" * 50)
        print(f"   OVERALL RESULT: {passed_tests}/{total_tests} tests passed")
        
        if passed_tests == total_tests:
            print("   🎯 AUTO-LEARNING SYSTEM: FULLY VALIDATED ✅")
            print("   🚀 PRODUCTION READY: All systems operational")
        elif passed_tests >= total_tests * 0.8:
            print("   ⚠️ AUTO-LEARNING SYSTEM: MOSTLY VALIDATED")
            print("   🔧 MINOR ISSUES: Some components need attention")
        else:
            print("   ❌ AUTO-LEARNING SYSTEM: VALIDATION FAILED")
            print("   🚨 CRITICAL ISSUES: System needs major fixes")
        
        conn.close()
        return validation_results
        
    except Exception as e:
        print(f"❌ Critical error during validation: {e}")
        import traceback
        traceback.print_exc()
        return validation_results

if __name__ == "__main__":
    results = comprehensive_auto_learning_validation()
    
    # Exit with appropriate code
    passed_tests = sum(results.values())
    total_tests = len(results)
    
    if passed_tests == total_tests:
        sys.exit(0)  # All tests passed
    else:
        sys.exit(1)  # Some tests failed
