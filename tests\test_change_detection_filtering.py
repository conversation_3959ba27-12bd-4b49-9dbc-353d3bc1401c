#!/usr/bin/env python3
"""
COMPREHENSIVE TEST SUITE: Change Detection Filtering System
Tests all change detection scenarios, dual filtering combinations, and performance impact.
"""

import unittest
import sys
import os
import time
from unittest.mock import Mock, patch

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.dictionary_manager import PayrollDictionaryManager
from core.phased_process_manager import PhasedProcessManager

class TestChangeDetectionFiltering(unittest.TestCase):
    """Comprehensive test suite for change detection filtering system"""

    def setUp(self):
        """Set up test environment"""
        self.dict_manager = PayrollDictionaryManager(debug=True)
        
        # Test data for various scenarios
        self.test_sections = ['EARNINGS', 'DEDUCTIONS', 'PERSONAL DETAILS', 'LOANS', 'EMPLOYERS CONTRIBUTION']
        self.test_items = ['BASIC SALARY', 'INCOME TAX', 'EMPLOYEE NAME', 'RENT ADVANCE', 'SSF EMPLOYER']
        self.change_types = ['NEW', 'INCREASE', 'DECREASE', 'REMOVED', 'NO_CHANGE']
        
        # Sample change records for testing
        self.sample_changes = [
            {
                'section_name': 'EARNINGS',
                'item_label': 'BASIC SALARY',
                'change_type': 'INCREASE',
                'previous_value': '1000.00',
                'current_value': '1100.00'
            },
            {
                'section_name': 'DEDUCTIONS',
                'item_label': 'INCOME TAX',
                'change_type': 'NEW',
                'previous_value': None,
                'current_value': '150.00'
            },
            {
                'section_name': 'PERSONAL DETAILS',
                'item_label': 'EMPLOYEE NAME',
                'change_type': 'NO_CHANGE',
                'previous_value': 'John Doe',
                'current_value': 'John Doe'
            },
            {
                'section_name': 'LOANS',
                'item_label': 'RENT ADVANCE',
                'change_type': 'DECREASE',
                'previous_value': '5000.00',
                'current_value': '4500.00'
            },
            {
                'section_name': 'EMPLOYERS CONTRIBUTION',
                'item_label': 'SSF EMPLOYER',
                'change_type': 'REMOVED',
                'previous_value': '200.00',
                'current_value': None
            }
        ]

    def test_individual_change_type_filtering(self):
        """Test filtering for each individual change type"""
        print("\n🧪 Testing Individual Change Type Filtering:")
        
        for change_type in self.change_types:
            with self.subTest(change_type=change_type):
                # Test with default settings (should include most types except NO_CHANGE)
                result = self.dict_manager.should_include_change_type(
                    'EARNINGS', 'BASIC SALARY', change_type
                )
                
                expected = change_type != 'NO_CHANGE'  # NO_CHANGE defaults to False
                self.assertEqual(result, expected, 
                    f"Change type {change_type} filtering failed")
                print(f"   ✅ {change_type}: {'INCLUDED' if result else 'EXCLUDED'}")

    def test_dual_filtering_combinations(self):
        """Test all combinations of include_in_report and change detection filtering"""
        print("\n🧪 Testing Dual Filtering Combinations:")
        
        test_combinations = [
            # (include_in_report, include_new, include_increase, include_decrease, include_removed, include_no_change)
            (True, True, True, True, True, True),    # All enabled
            (True, False, True, True, True, False),  # NEW and NO_CHANGE disabled
            (False, True, True, True, True, True),   # include_in_report disabled
            (True, True, False, False, True, False), # Only NEW and REMOVED enabled
            (False, False, False, False, False, False), # All disabled
        ]
        
        for i, combo in enumerate(test_combinations):
            with self.subTest(combination=i):
                include_in_report, include_new, include_increase, include_decrease, include_removed, include_no_change = combo
                
                # Mock the dictionary manager settings
                with patch.object(self.dict_manager, 'should_include_in_report', return_value=include_in_report):
                    with patch.object(self.dict_manager, 'should_include_change_type') as mock_change_type:
                        # Configure change type responses
                        def change_type_side_effect(section, item, change_type):
                            type_map = {
                                'NEW': include_new,
                                'INCREASE': include_increase,
                                'DECREASE': include_decrease,
                                'REMOVED': include_removed,
                                'NO_CHANGE': include_no_change
                            }
                            return type_map.get(change_type, True)
                        
                        mock_change_type.side_effect = change_type_side_effect
                        
                        # Test comprehensive filtering
                        for change in self.sample_changes:
                            result = self.dict_manager.should_include_change(
                                change['section_name'], 
                                change['item_label'], 
                                change['change_type']
                            )
                            
                            # Expected result: both filters must pass
                            expected = include_in_report and change_type_side_effect(
                                change['section_name'], 
                                change['item_label'], 
                                change['change_type']
                            )
                            
                            self.assertEqual(result, expected,
                                f"Dual filtering failed for combination {i}, change {change['change_type']}")
                
                print(f"   ✅ Combination {i}: include_in_report={include_in_report}, filters={combo[1:]}")

    def test_section_classification_preservation(self):
        """Test that section classifications are preserved through filtering"""
        print("\n🧪 Testing Section Classification Preservation:")
        
        for section in self.test_sections:
            for item in self.test_items:
                for change_type in self.change_types:
                    with self.subTest(section=section, item=item, change_type=change_type):
                        # Test that section and item names are preserved in filtering logic
                        try:
                            result = self.dict_manager.should_include_change(section, item, change_type)
                            
                            # Verify that the method doesn't corrupt section/item names
                            self.assertIsInstance(result, bool, 
                                f"Filtering should return boolean for {section}.{item}.{change_type}")
                            
                        except Exception as e:
                            self.fail(f"Section classification corrupted for {section}.{item}.{change_type}: {e}")
        
        print("   ✅ All section classifications preserved")

    def test_performance_impact(self):
        """Measure performance impact of change detection filtering"""
        print("\n🧪 Testing Performance Impact:")
        
        # Create large dataset for performance testing
        large_dataset = []
        for i in range(1000):
            for change in self.sample_changes:
                large_dataset.append({
                    **change,
                    'employee_id': f'EMP{i:04d}',
                    'id': len(large_dataset)
                })
        
        # Test performance without filtering
        start_time = time.time()
        unfiltered_count = len(large_dataset)
        no_filter_time = time.time() - start_time
        
        # Test performance with filtering
        start_time = time.time()
        
        # Mock the phased process manager's filtering method
        mock_manager = Mock()
        mock_manager.debug = False
        mock_manager._debug_print = Mock()
        
        # Import the actual filtering method
        from core.phased_process_manager import PhasedProcessManager
        filtering_method = PhasedProcessManager._apply_change_detection_filtering
        
        try:
            filtered_dataset = filtering_method(mock_manager, large_dataset)
            filter_time = time.time() - start_time
            filtered_count = len(filtered_dataset)
            
            # Calculate performance metrics
            overhead_ms = (filter_time - no_filter_time) * 1000
            throughput = len(large_dataset) / filter_time if filter_time > 0 else float('inf')
            
            print(f"   📊 Dataset size: {len(large_dataset)} records")
            print(f"   📊 Filtering time: {filter_time:.4f}s")
            print(f"   📊 Overhead: {overhead_ms:.2f}ms")
            print(f"   📊 Throughput: {throughput:.0f} records/second")
            print(f"   📊 Filtered: {unfiltered_count} → {filtered_count} records")
            
            # Performance assertions
            self.assertLess(filter_time, 5.0, "Filtering should complete within 5 seconds")
            self.assertLess(overhead_ms, 1000, "Overhead should be less than 1 second")
            self.assertGreater(throughput, 100, "Should process at least 100 records/second")
            
        except Exception as e:
            print(f"   ⚠️ Performance test failed: {e}")
            # Don't fail the test for performance issues, just warn
            pass

    def test_no_conflicts_between_filtering_systems(self):
        """Test that include_in_report and change detection filtering don't conflict"""
        print("\n🧪 Testing No Conflicts Between Filtering Systems:")
        
        conflict_scenarios = [
            # Test scenarios where filters might conflict
            {'include_in_report': True, 'change_filters': {'NEW': False}},
            {'include_in_report': False, 'change_filters': {'NEW': True}},
            {'include_in_report': True, 'change_filters': {'NO_CHANGE': True}},
            {'include_in_report': False, 'change_filters': {'NO_CHANGE': False}},
        ]
        
        for i, scenario in enumerate(conflict_scenarios):
            with self.subTest(scenario=i):
                include_in_report = scenario['include_in_report']
                change_filters = scenario['change_filters']
                
                # Mock both filtering systems
                with patch.object(self.dict_manager, 'should_include_in_report', return_value=include_in_report):
                    with patch.object(self.dict_manager, 'should_include_change_type') as mock_change_type:
                        
                        def change_type_side_effect(section, item, change_type):
                            return change_filters.get(change_type, True)
                        
                        mock_change_type.side_effect = change_type_side_effect
                        
                        # Test that comprehensive filtering behaves predictably
                        for change_type in change_filters.keys():
                            result = self.dict_manager.should_include_change(
                                'EARNINGS', 'BASIC SALARY', change_type
                            )
                            
                            # Expected: both filters must pass (AND logic)
                            expected = include_in_report and change_filters[change_type]
                            
                            self.assertEqual(result, expected,
                                f"Conflict detected in scenario {i} for {change_type}")
                
                print(f"   ✅ Scenario {i}: No conflicts detected")

    def test_edge_cases(self):
        """Test edge cases and error handling"""
        print("\n🧪 Testing Edge Cases:")
        
        edge_cases = [
            # (section, item, change_type, description)
            ('', '', '', 'Empty strings'),
            (None, None, None, 'None values'),
            ('UNKNOWN_SECTION', 'UNKNOWN_ITEM', 'UNKNOWN_TYPE', 'Unknown values'),
            ('EARNINGS', 'BASIC SALARY', 'invalid_type', 'Invalid change type'),
        ]
        
        for section, item, change_type, description in edge_cases:
            with self.subTest(case=description):
                try:
                    # Should not raise exceptions
                    result = self.dict_manager.should_include_change(section, item, change_type)
                    self.assertIsInstance(result, bool, f"Should return boolean for {description}")
                    print(f"   ✅ {description}: Handled gracefully")
                    
                except Exception as e:
                    self.fail(f"Edge case '{description}' caused exception: {e}")

if __name__ == '__main__':
    print("🚀 CHANGE DETECTION FILTERING - COMPREHENSIVE TEST SUITE")
    print("=" * 70)
    
    # Run tests with verbose output
    unittest.main(verbosity=2, exit=False)
    
    print("\n" + "=" * 70)
    print("✅ COMPREHENSIVE TEST SUITE COMPLETED")
    print("🎯 Change detection filtering system validated")
