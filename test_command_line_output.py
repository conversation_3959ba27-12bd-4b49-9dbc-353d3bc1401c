#!/usr/bin/env python3
"""
Test Command Line Output Issues
"""

import sys
import os
import json

def test_command_line_output():
    """Test the command line output issues"""
    print("🔍 TESTING COMMAND LINE OUTPUT ISSUES")
    print("=" * 50)
    
    try:
        # 1. Test get_pre_reporting_data method directly
        print("1. 🧪 TESTING get_pre_reporting_data METHOD DIRECTLY:")
        
        sys.path.append(os.path.dirname(__file__))
        from core.phased_process_manager import PhasedProcessManager
        
        manager = PhasedProcessManager(debug_mode=False)
        result = manager.get_pre_reporting_data()
        
        print(f"   📊 Direct method result: {result}")
        print(f"   📊 Result type: {type(result)}")
        print(f"   📊 Success: {result.get('success') if isinstance(result, dict) else 'Not a dict'}")
        
        # 2. Test the command line interface simulation
        print("\n2. 🧪 TESTING COMMAND LINE INTERFACE SIMULATION:")
        
        # Simulate the command line call
        original_argv = sys.argv
        sys.argv = ['phased_process_manager.py', 'get-latest-pre-reporting-data']
        
        try:
            # Import and call the main function
            from core.phased_process_manager import main
            
            # Capture stdout
            import io
            from contextlib import redirect_stdout
            
            captured_output = io.StringIO()
            with redirect_stdout(captured_output):
                main()
            
            output = captured_output.getvalue()
            
            print(f"   📊 Command line output: '{output}'")
            print(f"   📊 Output length: {len(output)}")
            
            if output.strip():
                try:
                    parsed = json.loads(output.strip())
                    print(f"   ✅ Valid JSON output: {parsed}")
                except json.JSONDecodeError as e:
                    print(f"   ❌ Invalid JSON: {e}")
            else:
                print("   ❌ Empty output from command line interface")
        
        finally:
            sys.argv = original_argv
        
        # 3. Test populate_tables command
        print("\n3. 🧪 TESTING POPULATE_TABLES COMMAND:")
        
        try:
            import bank_adviser_tracker_operations
            
            # Simulate the command
            original_argv = sys.argv
            sys.argv = ['bank_adviser_tracker_operations.py', 'populate_tables']
            
            # Capture stdout
            captured_output = io.StringIO()
            with redirect_stdout(captured_output):
                # Call the main function
                if hasattr(bank_adviser_tracker_operations, 'main'):
                    bank_adviser_tracker_operations.main()
                else:
                    # Call the populate function directly
                    result = bank_adviser_tracker_operations.populate_bank_adviser_tables()
                    print(json.dumps(result))
            
            output = captured_output.getvalue()
            
            print(f"   📊 Populate tables output: '{output}'")
            print(f"   📊 Output length: {len(output)}")
            
            if output.strip():
                try:
                    parsed = json.loads(output.strip())
                    print(f"   ✅ Valid JSON output: {parsed}")
                except json.JSONDecodeError as e:
                    print(f"   ❌ Invalid JSON: {e}")
            else:
                print("   ❌ Empty output from populate tables command")
            
            sys.argv = original_argv
            
        except Exception as e:
            print(f"   ❌ Populate tables test failed: {e}")
        
        # 4. Test with subprocess (how Electron actually calls it)
        print("\n4. 🧪 TESTING WITH SUBPROCESS (ELECTRON METHOD):")
        
        import subprocess
        
        # Test get-latest-pre-reporting-data
        try:
            result = subprocess.run([
                'python', 'core/phased_process_manager.py', 'get-latest-pre-reporting-data'
            ], capture_output=True, text=True, timeout=30)
            
            print(f"   📊 Subprocess return code: {result.returncode}")
            print(f"   📊 Subprocess stdout: '{result.stdout}'")
            print(f"   📊 Subprocess stderr: '{result.stderr}'")
            
            if result.stdout.strip():
                try:
                    parsed = json.loads(result.stdout.strip())
                    print(f"   ✅ Valid subprocess JSON: {parsed}")
                except json.JSONDecodeError as e:
                    print(f"   ❌ Invalid subprocess JSON: {e}")
            else:
                print("   ❌ Empty subprocess output")
        
        except Exception as e:
            print(f"   ❌ Subprocess test failed: {e}")
        
        # Test populate_tables
        try:
            result = subprocess.run([
                'python', 'bank_adviser_tracker_operations.py', 'populate_tables'
            ], capture_output=True, text=True, timeout=30)
            
            print(f"   📊 Populate subprocess return code: {result.returncode}")
            print(f"   📊 Populate subprocess stdout: '{result.stdout}'")
            print(f"   📊 Populate subprocess stderr: '{result.stderr}'")
            
            if result.stdout.strip():
                try:
                    parsed = json.loads(result.stdout.strip())
                    print(f"   ✅ Valid populate subprocess JSON: {parsed}")
                except json.JSONDecodeError as e:
                    print(f"   ❌ Invalid populate subprocess JSON: {e}")
            else:
                print("   ❌ Empty populate subprocess output")
        
        except Exception as e:
            print(f"   ❌ Populate subprocess test failed: {e}")
        
        # 5. Root cause analysis
        print("\n5. 🎯 ROOT CAUSE ANALYSIS:")
        print("   " + "=" * 40)
        
        print("   🔍 FINDINGS:")
        print("   • Direct method calls work correctly")
        print("   • Command line interface may have output issues")
        print("   • Subprocess calls (used by Electron) may be failing")
        print("   • JSON output may not be reaching stdout properly")
        
        print("\n   💡 LIKELY CAUSES:")
        print("   • Exception handling swallowing output")
        print("   • Debug output interfering with JSON")
        print("   • Buffering issues with stdout")
        print("   • Import errors in command line mode")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_command_line_output()
