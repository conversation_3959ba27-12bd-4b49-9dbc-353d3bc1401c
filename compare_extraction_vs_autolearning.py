#!/usr/bin/env python3
"""
CRITICAL ANALYSIS: Compare Extraction vs Auto-Learning Data
Compare section assignments between extraction and auto-learning to find corruption
"""

import sys
import os
import sqlite3
import json

def compare_extraction_vs_autolearning():
    """Compare section assignments between extraction and auto-learning data"""
    print("🔍 CRITICAL ANALYSIS: EXTRACTION vs AUTO-LEARNING SECTION ASSIGNMENTS")
    print("=" * 75)
    
    try:
        # 1. Connect to database
        db_path = r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Sessions
        auto_learning_session = "audit_session_1751025285_f593c99f"
        extraction_session = "audit_session_1751025285_42221d70"
        
        print(f"📋 Auto-learning session: {auto_learning_session}")
        print(f"📋 Extraction session: {extraction_session}")
        
        # 2. GET EXTRACTION DATA SECTIONS
        print("\n2. 📊 EXTRACTION DATA SECTIONS:")
        
        cursor.execute("""
            SELECT section_name, COUNT(DISTINCT item_label) as unique_items, COUNT(*) as total_occurrences
            FROM extracted_data 
            WHERE session_id = ? AND period_type = 'current'
            GROUP BY section_name
            ORDER BY unique_items DESC
        """, (extraction_session,))
        
        extraction_sections = cursor.fetchall()
        
        print("   📊 Extraction sections:")
        for section, unique_items, total_occurrences in extraction_sections:
            print(f"      {section}: {unique_items} unique items, {total_occurrences} total occurrences")
        
        # 3. GET AUTO-LEARNING DATA SECTIONS
        print("\n3. 📊 AUTO-LEARNING DATA SECTIONS:")
        
        cursor.execute("""
            SELECT section_name, COUNT(*) as item_count, AVG(confidence_score) as avg_confidence
            FROM auto_learning_results 
            WHERE session_id = ?
            GROUP BY section_name
            ORDER BY item_count DESC
        """, (auto_learning_session,))
        
        auto_learning_sections = cursor.fetchall()
        
        print("   📊 Auto-learning sections:")
        for section, item_count, avg_confidence in auto_learning_sections:
            print(f"      {section}: {item_count} items, avg confidence: {avg_confidence:.2f}")
        
        # 4. FIND COMMON ITEMS BETWEEN SESSIONS
        print("\n4. 🔄 FINDING COMMON ITEMS FOR COMPARISON:")
        
        # Get items that appear in both extraction and auto-learning
        cursor.execute("""
            SELECT DISTINCT e.item_label
            FROM extracted_data e
            WHERE e.session_id = ? AND e.period_type = 'current'
            AND EXISTS (
                SELECT 1 FROM auto_learning_results a 
                WHERE a.session_id = ? AND a.item_label = e.item_label
            )
            LIMIT 50
        """, (extraction_session, auto_learning_session))
        
        common_items = [row[0] for row in cursor.fetchall()]
        
        print(f"   📊 Found {len(common_items)} common items between sessions")
        
        if not common_items:
            print("   ❌ No common items found - sessions may be completely different")
            return False
        
        # 5. COMPARE SECTION ASSIGNMENTS FOR COMMON ITEMS
        print("\n5. 🔍 COMPARING SECTION ASSIGNMENTS:")
        
        mismatches = []
        matches = []
        
        for item in common_items[:20]:  # Test first 20 items
            # Get extraction section (most frequent)
            cursor.execute("""
                SELECT section_name, COUNT(*) as frequency
                FROM extracted_data 
                WHERE session_id = ? AND item_label = ? AND period_type = 'current'
                GROUP BY section_name
                ORDER BY frequency DESC
                LIMIT 1
            """, (extraction_session, item))
            
            extraction_result = cursor.fetchone()
            
            if not extraction_result:
                continue
                
            extraction_section = extraction_result[0]
            
            # Get auto-learning section
            cursor.execute("""
                SELECT section_name, confidence_score
                FROM auto_learning_results 
                WHERE session_id = ? AND item_label = ?
                LIMIT 1
            """, (auto_learning_session, item))
            
            auto_learning_result = cursor.fetchone()
            
            if not auto_learning_result:
                continue
                
            auto_learning_section = auto_learning_result[0]
            confidence = auto_learning_result[1]
            
            # Compare sections
            if extraction_section == auto_learning_section:
                matches.append((item, extraction_section, confidence))
                print(f"   ✅ MATCH: {item}")
                print(f"      Extraction: {extraction_section}")
                print(f"      Auto-learning: {auto_learning_section} (confidence: {confidence})")
            else:
                mismatches.append((item, extraction_section, auto_learning_section, confidence))
                print(f"   ❌ MISMATCH: {item}")
                print(f"      Extraction: {extraction_section}")
                print(f"      Auto-learning: {auto_learning_section} (confidence: {confidence})")
                print(f"      🚨 SECTION CORRUPTION DETECTED!")
        
        # 6. ANALYZE PATTERNS
        print(f"\n6. 📊 ANALYSIS RESULTS:")
        print(f"   ✅ Matches: {len(matches)}")
        print(f"   ❌ Mismatches: {len(mismatches)}")
        
        if mismatches:
            print(f"\n   🚨 CRITICAL ISSUE CONFIRMED:")
            print(f"      {len(mismatches)} items have incorrect section assignments in auto-learning")
            
            # Analyze mismatch patterns
            mismatch_patterns = {}
            for item, ext_section, auto_section, confidence in mismatches:
                pattern = f"{ext_section} → {auto_section}"
                if pattern not in mismatch_patterns:
                    mismatch_patterns[pattern] = []
                mismatch_patterns[pattern].append((item, confidence))
            
            print(f"\n   📊 Mismatch patterns:")
            for pattern, items in mismatch_patterns.items():
                avg_confidence = sum(conf for _, conf in items) / len(items)
                print(f"      {pattern}: {len(items)} items (avg confidence: {avg_confidence:.2f})")
                for item, conf in items[:3]:  # Show first 3 examples
                    print(f"         {item} (confidence: {conf})")
        
        # 7. INVESTIGATE THE ROOT CAUSE
        print(f"\n7. 🎯 ROOT CAUSE INVESTIGATION:")
        
        if mismatches:
            print("   🔍 Analyzing how sections are being reassigned...")
            
            # Check if there's a pattern in the auto-learning logic
            # Look at the _analyze_for_new_items method in phased_process_manager.py
            
            print("   📋 The issue is likely in phased_process_manager._analyze_for_new_items:")
            print("      • This method processes extracted data for auto-learning")
            print("      • It may be recalculating or reassigning sections")
            print("      • Instead of preserving the extraction section assignments")
            
            print("\n   🔧 REQUIRED FIX:")
            print("      1. Preserve original section from extraction data")
            print("      2. Do NOT recalculate sections in auto-learning phase")
            print("      3. Use extracted_data.section_name as authoritative source")
            print("      4. Only calculate confidence scores, not section assignments")
            
        else:
            print("   ✅ No section corruption detected")
            print("   💡 The issue may be in confidence calculation only")
        
        # 8. SHOW SPECIFIC EXAMPLES
        if mismatches:
            print(f"\n8. 📋 SPECIFIC EXAMPLES OF CORRUPTION:")
            
            for item, ext_section, auto_section, confidence in mismatches[:5]:
                print(f"\n   🔍 Item: {item}")
                
                # Show extraction details
                cursor.execute("""
                    SELECT section_name, COUNT(*) as frequency
                    FROM extracted_data 
                    WHERE session_id = ? AND item_label = ? AND period_type = 'current'
                    GROUP BY section_name
                    ORDER BY frequency DESC
                """, (extraction_session, item))
                
                extraction_details = cursor.fetchall()
                
                print(f"      📊 Extraction data:")
                for section, freq in extraction_details:
                    print(f"         {section}: {freq} occurrences")
                
                print(f"      📊 Auto-learning data:")
                print(f"         {auto_section}: confidence {confidence}")
                
                if len(extraction_details) > 1:
                    print(f"      ⚠️ Item appears in multiple sections in extraction")
                else:
                    print(f"      🚨 Clear section corruption: {ext_section} → {auto_section}")
        
        conn.close()
        
        # 9. FINAL VERDICT
        print(f"\n9. ⚖️ FINAL VERDICT:")
        
        if mismatches:
            corruption_rate = len(mismatches) / (len(matches) + len(mismatches)) * 100
            print(f"   🚨 SECTION CORRUPTION CONFIRMED")
            print(f"      Corruption rate: {corruption_rate:.1f}%")
            print(f"      {len(mismatches)} items have wrong sections")
            print(f"      Root cause: Auto-learning phase is reassigning sections")
            print(f"      Solution: Preserve extraction sections in auto-learning")
        else:
            print(f"   ✅ NO SECTION CORRUPTION DETECTED")
            print(f"      All sections are preserved correctly")
            print(f"      Issue may be confidence calculation only")
        
        return len(mismatches) > 0  # Return True if corruption found
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    corruption_found = compare_extraction_vs_autolearning()
    if corruption_found:
        print("\n🚨 CRITICAL: Section corruption detected - immediate fix required!")
    else:
        print("\n✅ No section corruption - sections are preserved correctly")
    sys.exit(1 if corruption_found else 0)
