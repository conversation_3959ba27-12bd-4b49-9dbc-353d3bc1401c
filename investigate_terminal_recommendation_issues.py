#!/usr/bin/env python3
"""
Investigate Terminal Recommendation Issues
Detailed analysis of remaining EXCLUDE functionality gaps
"""

import sys
import os
import sqlite3
import time

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def investigate_terminal_recommendation_issues():
    """Investigate specific issues causing terminal warnings"""
    print("🔍 INVESTIGATING TERMINAL RECOMMENDATION ISSUES")
    print("=" * 60)
    
    investigation_results = {
        'pre_reporting_table_filtering': False,
        'comparison_phase_filtering': False,
        'extraction_phase_analysis': False,
        'database_consistency': False,
        'performance_optimization': False,
        'end_to_end_validation': False
    }
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return investigation_results
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get current session
        sys.path.append(os.path.dirname(__file__))
        from core.session_manager import get_current_session_id
        current_session = get_current_session_id()
        print(f"🎯 Investigating session: {current_session}")
        
        # 1. Pre-reporting Table Filtering Investigation
        print("\n1. 📋 PRE-REPORTING TABLE FILTERING INVESTIGATION:")
        
        # Setup test exclusions
        test_exclusions = []
        cursor.execute("""
            SELECT DISTINCT cr.section_name, cr.item_label 
            FROM comparison_results cr
            WHERE cr.session_id = ?
            LIMIT 3
        """, (current_session,))
        
        test_items = cursor.fetchall()
        
        if test_items:
            for section, item in test_items:
                # Set items to exclude
                cursor.execute("""
                    INSERT OR IGNORE INTO dictionary_sections (section_name) 
                    VALUES (?)
                """, (section,))
                
                cursor.execute("""
                    SELECT id FROM dictionary_sections WHERE section_name = ?
                """, (section,))
                section_id = cursor.fetchone()[0]
                
                cursor.execute("""
                    INSERT OR REPLACE INTO dictionary_items 
                    (item_name, section_id, include_in_report) 
                    VALUES (?, ?, 0)
                """, (item, section_id))
                
                test_exclusions.append((section, item))
            
            conn.commit()
            print(f"   ✅ Created {len(test_exclusions)} test exclusions")
            
            # Check if excluded items appear in pre_reporting_results table
            excluded_in_table = 0
            for section, item in test_exclusions:
                cursor.execute("""
                    SELECT COUNT(*) FROM pre_reporting_results pr
                    JOIN comparison_results cr ON pr.change_id = cr.id
                    WHERE cr.session_id = ? AND cr.section_name = ? AND cr.item_label = ?
                """, (current_session, section, item))
                
                count = cursor.fetchone()[0]
                excluded_in_table += count
                
                if count > 0:
                    print(f"   ❌ {section}.{item}: {count} entries in pre_reporting_results table")
            
            if excluded_in_table == 0:
                print("   ✅ No excluded items found in pre_reporting_results table")
                investigation_results['pre_reporting_table_filtering'] = True
            else:
                print(f"   ❌ {excluded_in_table} excluded items found in pre_reporting_results table")
                print("   🔧 ISSUE: pre_reporting_results table contains excluded items")
        
        # 2. Comparison Phase Filtering Investigation
        print("\n2. ⚖️ COMPARISON PHASE FILTERING INVESTIGATION:")
        
        # Check if comparison_results table has filtering applied during generation
        cursor.execute("""
            SELECT COUNT(*) FROM comparison_results cr
            LEFT JOIN dictionary_items di ON cr.item_label = di.item_name
            WHERE cr.session_id = ? AND di.include_in_report = 0
        """, (current_session,))
        
        excluded_in_comparison = cursor.fetchone()[0]
        
        if excluded_in_comparison == 0:
            print("   ✅ No excluded items found in comparison_results table")
            investigation_results['comparison_phase_filtering'] = True
        else:
            print(f"   ❌ {excluded_in_comparison} excluded items found in comparison_results table")
            print("   🔧 ISSUE: Comparison phase doesn't filter during generation")
        
        # 3. Extraction Phase Analysis
        print("\n3. 🔍 EXTRACTION PHASE ANALYSIS:")
        
        # This is informational - extraction phase filtering is complex
        print("   📊 Extraction phase filtering analysis:")
        print("   - Current: No filtering during extraction (by design)")
        print("   - Impact: All items extracted, filtered in later phases")
        print("   - Recommendation: Maintain current approach for data integrity")
        investigation_results['extraction_phase_analysis'] = True
        
        # 4. Database Consistency Check
        print("\n4. 🗄️ DATABASE CONSISTENCY CHECK:")
        
        # Check for orphaned dictionary items
        cursor.execute("""
            SELECT COUNT(*) FROM dictionary_items di
            LEFT JOIN dictionary_sections ds ON di.section_id = ds.id
            WHERE ds.id IS NULL
        """)
        
        orphaned_items = cursor.fetchone()[0]
        
        # Check for missing include_in_report values
        cursor.execute("""
            SELECT COUNT(*) FROM dictionary_items 
            WHERE include_in_report IS NULL
        """)
        
        null_include_values = cursor.fetchone()[0]
        
        if orphaned_items == 0 and null_include_values == 0:
            print("   ✅ Database consistency check passed")
            investigation_results['database_consistency'] = True
        else:
            print(f"   ❌ Database issues found:")
            if orphaned_items > 0:
                print(f"     - {orphaned_items} orphaned dictionary items")
            if null_include_values > 0:
                print(f"     - {null_include_values} items with NULL include_in_report")
        
        # 5. Performance Optimization Analysis
        print("\n5. ⚡ PERFORMANCE OPTIMIZATION ANALYSIS:")
        
        # Test query performance with and without filtering
        start_time = time.time()
        cursor.execute("""
            SELECT COUNT(*) FROM comparison_results 
            WHERE session_id = ?
        """, (current_session,))
        unfiltered_count = cursor.fetchone()[0]
        unfiltered_time = time.time() - start_time
        
        start_time = time.time()
        cursor.execute("""
            SELECT COUNT(*) FROM comparison_results cr
            LEFT JOIN dictionary_items di ON cr.item_label = di.item_name
            WHERE cr.session_id = ? 
              AND (di.include_in_report = 1 OR di.include_in_report IS NULL)
        """, (current_session,))
        filtered_count = cursor.fetchone()[0]
        filtered_time = time.time() - start_time
        
        reduction_percentage = ((unfiltered_count - filtered_count) / unfiltered_count) * 100 if unfiltered_count > 0 else 0
        performance_ratio = filtered_time / unfiltered_time if unfiltered_time > 0 else 1
        
        print(f"   📊 Unfiltered: {unfiltered_count} items in {unfiltered_time:.4f}s")
        print(f"   📊 Filtered: {filtered_count} items in {filtered_time:.4f}s")
        print(f"   📊 Data reduction: {reduction_percentage:.1f}%")
        print(f"   📊 Performance ratio: {performance_ratio:.2f}x")
        
        if reduction_percentage > 0 and performance_ratio < 3.0:
            print("   ✅ Performance optimization acceptable")
            investigation_results['performance_optimization'] = True
        else:
            print("   ❌ Performance optimization needs improvement")
        
        # 6. End-to-End Validation
        print("\n6. 🔄 END-TO-END VALIDATION:")
        
        try:
            from core.phased_process_manager import PhasedProcessManager
            from core.advanced_reporting_system import AdvancedReportingSystem
            
            # Test phased process manager
            manager = PhasedProcessManager(debug_mode=False)
            result = manager.get_pre_reporting_data(current_session)
            
            if result.get('success'):
                pre_reporting_data = result.get('data', [])
                excluded_found_pre = 0
                
                for item in pre_reporting_data:
                    for section, excluded_item in test_exclusions:
                        if (item.get('section_name') == section and 
                            item.get('item_label') == excluded_item):
                            excluded_found_pre += 1
                
                print(f"   📊 Pre-reporting API: {len(pre_reporting_data)} items, {excluded_found_pre} excluded found")
            
            # Test advanced reporting system
            reporting_system = AdvancedReportingSystem()
            test_data = {
                'comparison_results': [
                    {
                        'section_name': section,
                        'item_label': item,
                        'employee_id': 'TEST001',
                        'value': '100'
                    } for section, item in test_exclusions
                ] + [
                    {
                        'section_name': 'EARNINGS',
                        'item_label': 'BASIC SALARY',
                        'employee_id': 'TEST002',
                        'value': '1000'
                    }
                ]
            }
            
            filtered_data = reporting_system._apply_include_in_report_filter(test_data)
            excluded_found_report = 0
            
            for item in filtered_data['comparison_results']:
                for section, excluded_item in test_exclusions:
                    if (item.get('section_name') == section and 
                        item.get('item_label') == excluded_item):
                        excluded_found_report += 1
            
            print(f"   📊 Report generation: {len(filtered_data['comparison_results'])} items, {excluded_found_report} excluded found")
            
            if excluded_found_pre == 0 and excluded_found_report == 0:
                print("   ✅ End-to-end validation passed")
                investigation_results['end_to_end_validation'] = True
            else:
                print("   ❌ End-to-end validation failed")
                
        except Exception as e:
            print(f"   ❌ End-to-end validation error: {e}")
        
        # Cleanup
        print("\n7. 🔧 CLEANUP:")
        for section, item in test_exclusions:
            cursor.execute("""
                UPDATE dictionary_items 
                SET include_in_report = 1 
                WHERE item_name = ?
            """, (item,))
        conn.commit()
        print("   ✅ Restored test items to INCLUDE state")
        
        conn.close()
        
        # Summary
        print("\n8. 📋 INVESTIGATION SUMMARY:")
        print("   " + "=" * 50)
        
        passed_investigations = sum(investigation_results.values())
        total_investigations = len(investigation_results)
        
        for investigation_name, result in investigation_results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"   {investigation_name.replace('_', ' ').title()}: {status}")
        
        print("   " + "=" * 50)
        print(f"   OVERALL RESULT: {passed_investigations}/{total_investigations} investigations passed")
        
        # Identify specific issues
        print("\n9. 🎯 SPECIFIC ISSUES IDENTIFIED:")
        
        issues_found = []
        
        if not investigation_results['pre_reporting_table_filtering']:
            issues_found.append("Pre-reporting table contains excluded items")
        
        if not investigation_results['comparison_phase_filtering']:
            issues_found.append("Comparison phase doesn't filter during generation")
        
        if not investigation_results['database_consistency']:
            issues_found.append("Database consistency issues detected")
        
        if not investigation_results['performance_optimization']:
            issues_found.append("Performance optimization needs improvement")
        
        if not investigation_results['end_to_end_validation']:
            issues_found.append("End-to-end validation failed")
        
        if issues_found:
            for i, issue in enumerate(issues_found, 1):
                print(f"   {i}. ❌ {issue}")
        else:
            print("   ✅ No specific issues identified")
        
        return investigation_results, issues_found
        
    except Exception as e:
        print(f"❌ Critical error during investigation: {e}")
        import traceback
        traceback.print_exc()
        return investigation_results, ["Critical investigation error"]

if __name__ == "__main__":
    results, issues = investigate_terminal_recommendation_issues()
    
    # Exit with appropriate code
    if len(issues) == 0:
        sys.exit(0)  # No issues found
    else:
        sys.exit(1)  # Issues found
