#!/usr/bin/env python3
"""
Test script to verify the Dictionary Manager save operation fix
"""

import sqlite3
import os
import sys
import json
import tempfile
from datetime import datetime

def get_database_path():
    """Get the correct database path"""
    possible_paths = [
        'payroll_audit.db',
        'data/templar_payroll_auditor.db',
        'data/payroll_audit.db',
        'templar_payroll_auditor.db'
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    
    return 'payroll_audit.db'  # Default

def test_fallback_script():
    """Test the new fallback script"""
    print("🧪 TESTING FALLBACK SCRIPT")
    print("-" * 40)
    
    # Check if fallback script exists
    fallback_path = 'core/dictionary_save_fallback.py'
    if not os.path.exists(fallback_path):
        print(f"   ❌ Fallback script not found: {fallback_path}")
        return False
    
    print(f"   ✅ Fallback script exists: {fallback_path}")
    
    # Test the fallback script with sample data
    test_dictionary = {
        "TEST_SECTION": {
            "items": {
                "TEST_ITEM": {
                    "format": "text",
                    "value_format": "text",
                    "include_in_report": True,
                    "include_new": True,
                    "include_increase": True,
                    "include_decrease": True,
                    "include_removed": True,
                    "include_no_change": False,
                    "standard_key": "TEST_ITEM",
                    "standardized_name": "TEST_ITEM",
                    "is_fixed": False,
                    "variations": ["text"],
                    "validation_rules": {}
                }
            }
        }
    }
    
    try:
        # Create temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_file:
            json.dump(test_dictionary, temp_file)
            temp_file_path = temp_file.name
        
        # Test the fallback script
        import subprocess
        result = subprocess.run([
            'python', fallback_path, temp_file_path
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0 and result.stdout.strip() == 'true':
            print("   ✅ Fallback script test successful")
            return True
        else:
            print(f"   ❌ Fallback script test failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"   ❌ Fallback script test error: {e}")
        return False
    finally:
        # Clean up temp file
        try:
            os.unlink(temp_file_path)
        except:
            pass

def test_database_save_with_large_data():
    """Test database save with larger dictionary data"""
    print("\n🧪 TESTING DATABASE SAVE WITH LARGE DATA")
    print("-" * 40)
    
    db_path = get_database_path()
    if not os.path.exists(db_path):
        print(f"   ❌ Database not found: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Create a larger test dictionary
        large_dictionary = {}
        for section_num in range(5):
            section_name = f"TEST_SECTION_{section_num}"
            large_dictionary[section_name] = {"items": {}}
            
            for item_num in range(20):
                item_name = f"TEST_ITEM_{section_num}_{item_num}"
                large_dictionary[section_name]["items"][item_name] = {
                    "format": "text",
                    "value_format": "text",
                    "include_in_report": True,
                    "include_new": item_num % 2 == 0,  # Alternate values
                    "include_increase": item_num % 3 == 0,
                    "include_decrease": item_num % 4 == 0,
                    "include_removed": item_num % 5 == 0,
                    "include_no_change": item_num % 6 == 0,
                    "standard_key": item_name,
                    "standardized_name": item_name,
                    "is_fixed": False,
                    "variations": ["text"],
                    "validation_rules": {}
                }
        
        print(f"   📊 Testing with {len(large_dictionary)} sections, {sum(len(s['items']) for s in large_dictionary.values())} items")
        
        # Clear existing test data
        cursor.execute("DELETE FROM dictionary_items WHERE item_name LIKE 'TEST_ITEM_%'")
        cursor.execute("DELETE FROM dictionary_sections WHERE section_name LIKE 'TEST_SECTION_%'")
        conn.commit()
        
        # Test the save operation
        queries = []
        
        # Clear and insert sections
        for section_name, section_data in large_dictionary.items():
            queries.append({
                'sql': 'INSERT INTO dictionary_sections (section_name) VALUES (?)',
                'params': [section_name]
            })
            
            for item_name, item_data in section_data['items'].items():
                queries.append({
                    'sql': '''INSERT OR REPLACE INTO dictionary_items
                              (section_id, item_name, standard_key, format_type, value_format,
                               include_in_report, include_new, include_increase, include_decrease,
                               include_removed, include_no_change, is_fixed, validation_rules)
                              VALUES ((SELECT id FROM dictionary_sections WHERE section_name = ?),
                                      ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                    'params': [
                        section_name, item_name, item_data.get('standardized_name', item_name),
                        item_data.get('format', 'text'), item_data.get('value_format', 'text'),
                        1 if item_data.get('include_in_report') else 0,
                        1 if item_data.get('include_new') else 0,
                        1 if item_data.get('include_increase') else 0,
                        1 if item_data.get('include_decrease') else 0,
                        1 if item_data.get('include_removed') else 0,
                        1 if item_data.get('include_no_change') else 0,
                        1 if item_data.get('is_fixed') else 0,
                        json.dumps(item_data.get('validation_rules', {}))
                    ]
                })
        
        # Execute all queries
        for query in queries:
            cursor.execute(query['sql'], query['params'])
        
        conn.commit()
        
        # Verify the save
        cursor.execute("SELECT COUNT(*) FROM dictionary_sections WHERE section_name LIKE 'TEST_SECTION_%'")
        section_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM dictionary_items WHERE item_name LIKE 'TEST_ITEM_%'")
        item_count = cursor.fetchone()[0]
        
        expected_sections = len(large_dictionary)
        expected_items = sum(len(s['items']) for s in large_dictionary.values())
        
        if section_count == expected_sections and item_count == expected_items:
            print(f"   ✅ Large data save successful: {section_count} sections, {item_count} items")
            success = True
        else:
            print(f"   ❌ Large data save failed: expected {expected_sections}/{expected_items}, got {section_count}/{item_count}")
            success = False
        
        # Clean up test data
        cursor.execute("DELETE FROM dictionary_items WHERE item_name LIKE 'TEST_ITEM_%'")
        cursor.execute("DELETE FROM dictionary_sections WHERE section_name LIKE 'TEST_SECTION_%'")
        conn.commit()
        
        conn.close()
        return success
        
    except Exception as e:
        print(f"   ❌ Large data test failed: {e}")
        return False

def test_change_detection_persistence():
    """Test that change detection toggles persist correctly"""
    print("\n🧪 TESTING CHANGE DETECTION PERSISTENCE")
    print("-" * 40)
    
    db_path = get_database_path()
    if not os.path.exists(db_path):
        print(f"   ❌ Database not found: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Test various toggle combinations
        test_cases = [
            {"include_new": True, "include_increase": False, "include_decrease": True, "include_removed": False, "include_no_change": True},
            {"include_new": False, "include_increase": True, "include_decrease": False, "include_removed": True, "include_no_change": False},
            {"include_new": True, "include_increase": True, "include_decrease": True, "include_removed": True, "include_no_change": True},
            {"include_new": False, "include_increase": False, "include_decrease": False, "include_removed": False, "include_no_change": False}
        ]
        
        success_count = 0
        
        for i, test_case in enumerate(test_cases):
            test_section = f"TOGGLE_TEST_SECTION_{i}"
            test_item = f"TOGGLE_TEST_ITEM_{i}"
            
            # Clean up
            cursor.execute("DELETE FROM dictionary_items WHERE item_name = ?", (test_item,))
            cursor.execute("DELETE FROM dictionary_sections WHERE section_name = ?", (test_section,))
            
            # Insert test data
            cursor.execute("INSERT INTO dictionary_sections (section_name) VALUES (?)", (test_section,))
            cursor.execute("SELECT id FROM dictionary_sections WHERE section_name = ?", (test_section,))
            section_id = cursor.fetchone()[0]
            
            cursor.execute("""
                INSERT INTO dictionary_items
                (section_id, item_name, standard_key, format_type, value_format,
                 include_in_report, include_new, include_increase, include_decrease,
                 include_removed, include_no_change, is_fixed, validation_rules)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                section_id, test_item, test_item, 'text', 'text', 1,
                1 if test_case['include_new'] else 0,
                1 if test_case['include_increase'] else 0,
                1 if test_case['include_decrease'] else 0,
                1 if test_case['include_removed'] else 0,
                1 if test_case['include_no_change'] else 0,
                0, '{}'
            ))
            
            conn.commit()
            
            # Verify persistence
            cursor.execute("""
                SELECT include_new, include_increase, include_decrease, include_removed, include_no_change
                FROM dictionary_items WHERE item_name = ?
            """, (test_item,))
            
            result = cursor.fetchone()
            expected = (
                1 if test_case['include_new'] else 0,
                1 if test_case['include_increase'] else 0,
                1 if test_case['include_decrease'] else 0,
                1 if test_case['include_removed'] else 0,
                1 if test_case['include_no_change'] else 0
            )
            
            if result == expected:
                success_count += 1
                print(f"   ✅ Test case {i+1}: Toggle persistence successful")
            else:
                print(f"   ❌ Test case {i+1}: Expected {expected}, got {result}")
            
            # Clean up
            cursor.execute("DELETE FROM dictionary_items WHERE item_name = ?", (test_item,))
            cursor.execute("DELETE FROM dictionary_sections WHERE section_name = ?", (test_section,))
        
        conn.commit()
        conn.close()
        
        if success_count == len(test_cases):
            print(f"   ✅ All {success_count} toggle persistence tests passed")
            return True
        else:
            print(f"   ❌ Only {success_count}/{len(test_cases)} toggle persistence tests passed")
            return False
        
    except Exception as e:
        print(f"   ❌ Toggle persistence test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 DICTIONARY SAVE FIX VERIFICATION")
    print("=" * 50)
    
    # Run all tests
    fallback_test = test_fallback_script()
    large_data_test = test_database_save_with_large_data()
    toggle_test = test_change_detection_persistence()
    
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS SUMMARY:")
    print(f"   Fallback Script Test: {'✅ PASSED' if fallback_test else '❌ FAILED'}")
    print(f"   Large Data Save Test: {'✅ PASSED' if large_data_test else '❌ FAILED'}")
    print(f"   Toggle Persistence Test: {'✅ PASSED' if toggle_test else '❌ FAILED'}")
    
    all_passed = fallback_test and large_data_test and toggle_test
    
    if all_passed:
        print("\n✅ ALL DICTIONARY SAVE FIXES VERIFIED SUCCESSFULLY!")
        print("🎯 The Dictionary Manager save operation should now work correctly")
    else:
        print("\n⚠️ SOME TESTS FAILED - ADDITIONAL FIXES MAY BE NEEDED")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
