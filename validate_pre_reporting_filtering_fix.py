#!/usr/bin/env python3
"""
Validate Pre-reporting Filtering Fix
Comprehensive test to verify the pre-reporting phase filtering implementation
"""

import sys
import os
import sqlite3
import time

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def validate_pre_reporting_filtering_fix():
    """Validate the pre-reporting filtering fix implementation"""
    print("🔍 VALIDATING PRE-REPORTING FILTERING FIX")
    print("=" * 60)
    
    test_results = {
        'setup_exclusions': False,
        'pre_reporting_api_filtering': False,
        'fallback_query_filtering': False,
        'consistency_with_ui_display': False,
        'performance_impact': False,
        'data_reduction_achieved': False
    }
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return test_results
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get current session
        sys.path.append(os.path.dirname(__file__))
        from core.session_manager import get_current_session_id
        current_session = get_current_session_id()
        print(f"🎯 Testing session: {current_session}")
        
        # 1. Setup Test Exclusions
        print("\n1. 🔧 SETTING UP TEST EXCLUSIONS:")
        
        # Find items to exclude for testing
        cursor.execute("""
            SELECT DISTINCT cr.section_name, cr.item_label 
            FROM comparison_results cr
            WHERE cr.session_id = ?
            LIMIT 5
        """, (current_session,))
        
        test_items = cursor.fetchall()
        excluded_items = []
        
        if test_items:
            # Select 3 items to exclude for testing
            items_to_exclude = test_items[:3]
            
            for section, item in items_to_exclude:
                # Check if item exists in dictionary
                cursor.execute("""
                    SELECT id FROM dictionary_items 
                    WHERE item_name = ? AND section_id = (
                        SELECT id FROM dictionary_sections WHERE section_name = ?
                    )
                """, (item, section))
                
                existing = cursor.fetchone()
                
                if existing:
                    # Update existing item to exclude
                    cursor.execute("""
                        UPDATE dictionary_items 
                        SET include_in_report = 0 
                        WHERE id = ?
                    """, (existing[0],))
                    excluded_items.append((section, item))
                    print(f"   ✅ Excluded existing item: {section}.{item}")
                else:
                    # Insert new item as excluded
                    cursor.execute("""
                        INSERT OR IGNORE INTO dictionary_sections (section_name) 
                        VALUES (?)
                    """, (section,))
                    
                    cursor.execute("""
                        SELECT id FROM dictionary_sections WHERE section_name = ?
                    """, (section,))
                    section_id = cursor.fetchone()[0]
                    
                    cursor.execute("""
                        INSERT INTO dictionary_items (item_name, section_id, include_in_report) 
                        VALUES (?, ?, 0)
                    """, (item, section_id))
                    excluded_items.append((section, item))
                    print(f"   ✅ Created excluded item: {section}.{item}")
            
            conn.commit()
            test_results['setup_exclusions'] = len(excluded_items) > 0
            print(f"   📊 Total items excluded for testing: {len(excluded_items)}")
        else:
            print("   ⚠️ No comparison results found for exclusion testing")
            return test_results
        
        # 2. Test Pre-reporting API Filtering
        print("\n2. 🔄 TESTING PRE-REPORTING API FILTERING:")
        
        try:
            from core.phased_process_manager import PhasedProcessManager
            
            manager = PhasedProcessManager(debug_mode=False)
            
            # Test get_pre_reporting_data method
            result = manager.get_pre_reporting_data(current_session)
            
            if result.get('success'):
                data = result.get('data', [])
                print(f"   📊 Pre-reporting data returned: {len(data)} items")
                
                # Check if excluded items are present
                excluded_found = 0
                for item in data:
                    for section, excluded_item in excluded_items:
                        if (item.get('section_name') == section and 
                            item.get('item_label') == excluded_item):
                            excluded_found += 1
                            print(f"   ❌ Found excluded item: {section}.{excluded_item}")
                
                if excluded_found == 0:
                    print("   ✅ No excluded items found in pre-reporting data")
                    test_results['pre_reporting_api_filtering'] = True
                else:
                    print(f"   ❌ {excluded_found} excluded items found in pre-reporting data")
            else:
                print(f"   ❌ Pre-reporting API failed: {result.get('error')}")
                
        except Exception as e:
            print(f"   ❌ Pre-reporting API test failed: {e}")
        
        # 3. Test Fallback Query Filtering
        print("\n3. 🔄 TESTING FALLBACK QUERY FILTERING:")
        
        try:
            # Test the fallback query directly
            cursor.execute("""
                SELECT pr.change_id as id, 'EMPLOYEE_' || pr.change_id as employee_id,
                       'Employee ' || pr.change_id as employee_name, 
                       COALESCE(cr.section_name, 'Unknown') as section_name,
                       COALESCE(cr.item_label, 'Change Item') as item_label, 
                       'Previous' as previous_value, 'Current' as current_value, 
                       'MODIFIED' as change_type, 'Medium' as priority,
                       pr.bulk_category, pr.bulk_size, pr.selected_for_report
                FROM pre_reporting_results pr
                LEFT JOIN comparison_results cr ON pr.change_id = cr.id
                LEFT JOIN dictionary_items di ON cr.item_label = di.item_name
                WHERE pr.session_id = ?
                  AND (di.include_in_report = 1 OR di.include_in_report IS NULL)
                ORDER BY pr.bulk_category, pr.change_id
            """, (current_session,))
            
            fallback_results = cursor.fetchall()
            print(f"   📊 Fallback query returned: {len(fallback_results)} items")
            
            # Check if excluded items are present
            excluded_found_fallback = 0
            for row in fallback_results:
                section_name = row[3] if len(row) > 3 else ''
                item_label = row[4] if len(row) > 4 else ''
                
                for section, excluded_item in excluded_items:
                    if section_name == section and item_label == excluded_item:
                        excluded_found_fallback += 1
                        print(f"   ❌ Found excluded item in fallback: {section}.{excluded_item}")
            
            if excluded_found_fallback == 0:
                print("   ✅ No excluded items found in fallback query")
                test_results['fallback_query_filtering'] = True
            else:
                print(f"   ❌ {excluded_found_fallback} excluded items found in fallback query")
                
        except Exception as e:
            print(f"   ❌ Fallback query test failed: {e}")
        
        # 4. Test Consistency with UI Display
        print("\n4. 🖥️ TESTING CONSISTENCY WITH UI DISPLAY:")
        
        try:
            # Test _load_selected_changes_for_reporting method (UI Display)
            manager.session_id = current_session
            ui_changes = manager._load_selected_changes_for_reporting()
            
            print(f"   📊 UI display data: {len(ui_changes)} items")
            
            # Check if excluded items are present in UI data
            excluded_found_ui = 0
            for change in ui_changes:
                for section, excluded_item in excluded_items:
                    if (change.get('section_name') == section and 
                        change.get('item_label') == excluded_item):
                        excluded_found_ui += 1
                        print(f"   ❌ Found excluded item in UI: {section}.{excluded_item}")
            
            if excluded_found_ui == 0:
                print("   ✅ No excluded items found in UI display data")
                
                # Check consistency between pre-reporting and UI filtering
                if (test_results['pre_reporting_api_filtering'] and 
                    excluded_found_ui == 0):
                    test_results['consistency_with_ui_display'] = True
                    print("   ✅ Pre-reporting and UI display filtering are consistent")
                else:
                    print("   ❌ Inconsistency between pre-reporting and UI display filtering")
            else:
                print(f"   ❌ {excluded_found_ui} excluded items found in UI display data")
                
        except Exception as e:
            print(f"   ❌ UI display consistency test failed: {e}")
        
        # 5. Test Performance Impact
        print("\n5. ⚡ TESTING PERFORMANCE IMPACT:")
        
        try:
            # Measure performance of filtered vs unfiltered queries
            
            # Unfiltered query (original)
            start_time = time.time()
            cursor.execute("""
                SELECT id, employee_id, employee_name, section_name, item_label,
                       previous_value, current_value, change_type, priority,
                       numeric_difference, percentage_change
                FROM comparison_results
                WHERE session_id = ?
                ORDER BY priority DESC, section_name, employee_id
            """, (current_session,))
            unfiltered_results = cursor.fetchall()
            unfiltered_time = time.time() - start_time
            
            # Filtered query (new)
            start_time = time.time()
            cursor.execute("""
                SELECT cr.id, cr.employee_id, cr.employee_name, cr.section_name, cr.item_label,
                       cr.previous_value, cr.current_value, cr.change_type, cr.priority,
                       cr.numeric_difference, cr.percentage_change
                FROM comparison_results cr
                LEFT JOIN dictionary_items di ON cr.item_label = di.item_name
                WHERE cr.session_id = ?
                  AND (di.include_in_report = 1 OR di.include_in_report IS NULL)
                ORDER BY cr.priority DESC, cr.section_name, cr.employee_id
            """, (current_session,))
            filtered_results = cursor.fetchall()
            filtered_time = time.time() - start_time
            
            print(f"   📊 Unfiltered query: {len(unfiltered_results)} items in {unfiltered_time:.4f}s")
            print(f"   📊 Filtered query: {len(filtered_results)} items in {filtered_time:.4f}s")
            
            # Calculate performance impact
            if unfiltered_time > 0:
                performance_ratio = filtered_time / unfiltered_time
                print(f"   📊 Performance ratio: {performance_ratio:.2f}x")
                
                if performance_ratio < 2.0:  # Less than 2x slower is acceptable
                    test_results['performance_impact'] = True
                    print("   ✅ Performance impact is acceptable")
                else:
                    print("   ⚠️ Performance impact may be significant")
            
            # Calculate data reduction
            if len(unfiltered_results) > 0:
                reduction_percentage = ((len(unfiltered_results) - len(filtered_results)) / 
                                      len(unfiltered_results)) * 100
                print(f"   📊 Data reduction: {reduction_percentage:.1f}%")
                
                if reduction_percentage > 0:
                    test_results['data_reduction_achieved'] = True
                    print("   ✅ Data reduction achieved through filtering")
                else:
                    print("   ⚠️ No data reduction achieved")
            
        except Exception as e:
            print(f"   ❌ Performance impact test failed: {e}")
        
        # 6. Cleanup - Restore Original State
        print("\n6. 🔧 CLEANUP - RESTORING ORIGINAL STATE:")
        
        for section, item in excluded_items:
            cursor.execute("""
                UPDATE dictionary_items 
                SET include_in_report = 1 
                WHERE item_name = ?
            """, (item,))
        conn.commit()
        print("   ✅ Restored all test items to INCLUDE state")
        
        conn.close()
        
        # 7. Final Results Summary
        print("\n7. 📋 VALIDATION RESULTS SUMMARY:")
        print("   " + "=" * 50)
        
        passed_tests = sum(test_results.values())
        total_tests = len(test_results)
        
        for test_name, result in test_results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"   {test_name.replace('_', ' ').title()}: {status}")
        
        print("   " + "=" * 50)
        print(f"   OVERALL RESULT: {passed_tests}/{total_tests} tests passed")
        
        if passed_tests == total_tests:
            print("   🎯 PRE-REPORTING FILTERING FIX: FULLY VALIDATED ✅")
            print("   🚀 PRODUCTION READY: All filtering mechanisms operational")
        elif passed_tests >= total_tests * 0.8:
            print("   ⚠️ PRE-REPORTING FILTERING FIX: MOSTLY VALIDATED")
            print("   🔧 MINOR ISSUES: Some components need attention")
        else:
            print("   ❌ PRE-REPORTING FILTERING FIX: VALIDATION FAILED")
            print("   🚨 CRITICAL ISSUES: Fix needs major revision")
        
        return test_results
        
    except Exception as e:
        print(f"❌ Critical error during validation: {e}")
        import traceback
        traceback.print_exc()
        return test_results

if __name__ == "__main__":
    results = validate_pre_reporting_filtering_fix()
    
    # Exit with appropriate code
    passed_tests = sum(results.values())
    total_tests = len(results)
    
    if passed_tests >= total_tests * 0.8:  # 80% pass threshold
        sys.exit(0)  # Acceptable validation level
    else:
        sys.exit(1)  # Validation issues found
