#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Improved Dictionary Save Fallback
Uses file-based communication instead of command line arguments
"""

import sys
import json
import tempfile
import os

# Set UTF-8 encoding for stdout/stderr
if sys.stdout.encoding != 'utf-8':
    sys.stdout.reconfigure(encoding='utf-8')
if sys.stderr.encoding != 'utf-8':
    sys.stderr.reconfigure(encoding='utf-8')

def save_dictionary_from_file(temp_file_path):
    """Save dictionary from temporary file"""
    try:
        # Read dictionary from temporary file
        with open(temp_file_path, 'r', encoding='utf-8') as f:
            dictionary = json.load(f)
        
        # Import the dictionary manager
        sys.path.append(os.path.dirname(__file__))
        from dictionary_manager import PayrollDictionaryManager
        
        # Create manager and save
        manager = PayrollDictionaryManager(debug=True)
        manager.dictionary = dictionary
        success = manager.save_dictionary()
        
        # Clean up temporary file
        os.unlink(temp_file_path)
        
        return success
        
    except Exception as e:
        # Safe error reporting
        try:
            print(f"Error in fallback save: {str(e)}", file=sys.stderr)
        except UnicodeEncodeError:
            print("Error in fallback save: Unicode encoding error", file=sys.stderr)
        return False

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python dictionary_save_fallback.py <temp_file_path>")
        sys.exit(1)
    
    temp_file_path = sys.argv[1]
    success = save_dictionary_from_file(temp_file_path)
    print("true" if success else "false")
