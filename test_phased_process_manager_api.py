#!/usr/bin/env python3
"""
Test Phased Process Manager Auto-Learning API
"""

import sys
import os
import json

def test_phased_process_manager_api():
    """Test the Phased Process Manager Auto-Learning API"""
    print("🔍 TESTING PHASED PROCESS MANAGER AUTO-LEARNING API")
    print("=" * 60)
    
    try:
        # Add core directory to path
        sys.path.append(os.path.join(os.path.dirname(__file__), 'core'))
        
        from core.phased_process_manager import PhasedProcessManager
        
        # Initialize the manager
        manager = PhasedProcessManager(debug_mode=True)
        print("✅ Phased Process Manager initialized")
        
        # Get current session
        try:
            from core.session_manager import get_current_session_id
            current_session = get_current_session_id()
            print(f"📋 Current session: {current_session}")
        except Exception as e:
            print(f"❌ Session manager error: {e}")
            current_session = None
        
        # Test get_pending_auto_learning_items method
        print("\n🔄 TESTING get_pending_auto_learning_items():")
        
        result = manager.get_pending_auto_learning_items()
        
        print(f"📊 API Result Success: {result.get('success')}")
        print(f"📊 API Result Count: {result.get('count', 0)}")
        
        if result.get('success'):
            data = result.get('data', [])
            print(f"📊 Pending items returned: {len(data)}")
            
            if len(data) > 0:
                print("\n📋 Sample pending items:")
                for i, item in enumerate(data[:5]):
                    print(f"   {i+1}. {item.get('section_name')}.{item.get('item_label')} (confidence: {item.get('confidence_score'):.2f})")
                
                # Check if current session items are included
                if current_session:
                    session_items = [item for item in data if item.get('session_id') == current_session]
                    print(f"\n📊 Items for current session {current_session}: {len(session_items)}")
                    
                    if session_items:
                        print("📋 Current session items:")
                        for i, item in enumerate(session_items[:5]):
                            print(f"   {i+1}. {item.get('section_name')}.{item.get('item_label')} (confidence: {item.get('confidence_score'):.2f})")
                
                print("\n✅ API is working correctly and returning data")
                print("🔍 The issue may be in the UI frontend or data refresh")
            else:
                print("\n❌ API returns success but no data")
                print("🔍 Check database query or filtering logic")
        else:
            print(f"\n❌ API failed: {result.get('error')}")
            if 'debug_info' in result:
                print(f"🔍 Debug info: {result['debug_info']}")
        
        # Test auto-learning stats
        print("\n📊 TESTING AUTO-LEARNING STATS:")
        
        try:
            # Check if there's a stats method
            if hasattr(manager, 'get_auto_learning_stats'):
                stats_result = manager.get_auto_learning_stats()
                print(f"Stats result: {stats_result}")
            else:
                print("No get_auto_learning_stats method found")
        except Exception as e:
            print(f"Stats test failed: {e}")
        
        # Test direct database query
        print("\n🔍 DIRECT DATABASE VERIFICATION:")
        
        try:
            # Query the database directly
            db_result = manager.db_manager.execute_query(
                '''SELECT COUNT(*) FROM auto_learning_results 
                   WHERE auto_approved = 0 AND dictionary_updated = 0'''
            )
            
            if db_result:
                total_pending = db_result[0]
                print(f"📊 Direct DB query - Total pending items: {total_pending}")
                
                if current_session:
                    session_result = manager.db_manager.execute_query(
                        '''SELECT COUNT(*) FROM auto_learning_results 
                           WHERE auto_approved = 0 AND dictionary_updated = 0 AND session_id = ?''',
                        (current_session,)
                    )
                    
                    if session_result:
                        session_pending = session_result[0]
                        print(f"📊 Direct DB query - Current session pending: {session_pending}")
            
        except Exception as e:
            print(f"❌ Direct database query failed: {e}")
        
        # Summary
        print("\n🎯 SUMMARY:")
        print("   " + "=" * 40)
        
        if result.get('success') and result.get('count', 0) > 0:
            print("   ✅ Phased Process Manager API is working correctly")
            print("   ✅ Data is available and being returned")
            print("   🔍 Issue is likely in the UI frontend or refresh mechanism")
            print("   💡 Recommendation: Check UI refresh, session filtering, or frontend data handling")
        elif result.get('success') and result.get('count', 0) == 0:
            print("   ⚠️ API works but returns no data")
            print("   🔍 Check database filtering or session management")
        else:
            print("   ❌ API is not working correctly")
            print("   🔍 Check database connectivity or method implementation")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_phased_process_manager_api()
