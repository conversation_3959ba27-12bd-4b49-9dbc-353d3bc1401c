#!/usr/bin/env python3
"""
Test script to verify change detection toggle persistence
"""

import sqlite3
import os
import sys

def test_change_detection_persistence():
    """Test that change detection columns are properly saved and loaded"""
    
    print("🧪 Testing Change Detection Toggle Persistence")
    print("=" * 50)
    
    # Find database
    db_path = 'payroll_audit.db'
    if not os.path.exists(db_path):
        print(f"❌ Database not found at {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if change detection columns exist
        cursor.execute("PRAGMA table_info(dictionary_items)")
        columns = [row[1] for row in cursor.fetchall()]
        
        required_columns = ['include_new', 'include_increase', 'include_decrease', 'include_removed', 'include_no_change']
        missing_columns = [col for col in required_columns if col not in columns]
        
        if missing_columns:
            print(f"❌ Missing columns: {missing_columns}")
            return False
        
        print("✅ All change detection columns exist")
        
        # Test data insertion
        test_section = 'TEST_SECTION'
        test_item = 'TEST_ITEM'
        
        # Clean up any existing test data
        cursor.execute("DELETE FROM dictionary_items WHERE item_name = ?", (test_item,))
        cursor.execute("DELETE FROM dictionary_sections WHERE section_name = ?", (test_section,))
        
        # Insert test section
        cursor.execute("INSERT INTO dictionary_sections (section_name) VALUES (?)", (test_section,))
        cursor.execute("SELECT id FROM dictionary_sections WHERE section_name = ?", (test_section,))
        section_id = cursor.fetchone()[0]
        
        # Insert test item with specific change detection settings
        cursor.execute('''
            INSERT INTO dictionary_items 
            (section_id, item_name, include_in_report, include_new, include_increase, 
             include_decrease, include_removed, include_no_change)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (section_id, test_item, 1, 0, 1, 0, 1, 1))  # Mixed settings for testing
        
        conn.commit()
        
        # Verify the data was saved correctly
        cursor.execute('''
            SELECT include_new, include_increase, include_decrease, include_removed, include_no_change
            FROM dictionary_items 
            WHERE item_name = ?
        ''', (test_item,))
        
        result = cursor.fetchone()
        expected = (0, 1, 0, 1, 1)
        
        if result == expected:
            print("✅ Change detection settings saved correctly")
        else:
            print(f"❌ Settings mismatch. Expected: {expected}, Got: {result}")
            return False
        
        # Test loading (simulate the loadDictionary function)
        cursor.execute('''
            SELECT di.*, ds.section_name
            FROM dictionary_items di
            JOIN dictionary_sections ds ON di.section_id = ds.id
            WHERE di.item_name = ?
        ''', (test_item,))
        
        row = cursor.fetchone()
        if row:
            # Simulate the loading logic from unified_database.js
            item_data = {
                'include_new': row[7] == 1,  # include_new column
                'include_increase': row[8] == 1,  # include_increase column
                'include_decrease': row[9] == 1,  # include_decrease column
                'include_removed': row[10] == 1,  # include_removed column
                'include_no_change': row[11] == 1,  # include_no_change column
            }
            
            expected_loaded = {
                'include_new': False,
                'include_increase': True,
                'include_decrease': False,
                'include_removed': True,
                'include_no_change': True,
            }
            
            if item_data == expected_loaded:
                print("✅ Change detection settings loaded correctly")
            else:
                print(f"❌ Loading mismatch. Expected: {expected_loaded}, Got: {item_data}")
                return False
        
        # Clean up test data
        cursor.execute("DELETE FROM dictionary_items WHERE item_name = ?", (test_item,))
        cursor.execute("DELETE FROM dictionary_sections WHERE section_name = ?", (test_section,))
        conn.commit()
        
        conn.close()
        
        print("✅ All tests passed - Change detection persistence working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_change_detection_persistence()
    sys.exit(0 if success else 1)
