#!/usr/bin/env python3
"""
Check Auto-Learning Table Structure
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def check_table_structure():
    """Check the actual structure of auto_learning_items table"""
    print("🔍 AUTO-LEARNING TABLE STRUCTURE CHECK")
    print("=" * 50)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='auto_learning_items'")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print("✅ auto_learning_items table exists")
            
            # Get table structure
            cursor.execute("PRAGMA table_info(auto_learning_items)")
            columns = cursor.fetchall()
            
            print("\n📋 TABLE STRUCTURE:")
            for col in columns:
                print(f"   {col[1]} ({col[2]}) - {'NOT NULL' if col[3] else 'NULL'} - Default: {col[4]}")
            
            # Check if table has any data
            cursor.execute("SELECT COUNT(*) FROM auto_learning_items")
            total_items = cursor.fetchone()[0]
            print(f"\n📊 Total items in table: {total_items}")
            
            if total_items > 0:
                # Show sample data
                cursor.execute("SELECT * FROM auto_learning_items LIMIT 3")
                sample_data = cursor.fetchall()
                
                print("\n📋 SAMPLE DATA:")
                for row in sample_data:
                    print(f"   {row}")
            
            # Check for other auto-learning related tables
            print("\n🔍 OTHER AUTO-LEARNING TABLES:")
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%auto%'")
            auto_tables = cursor.fetchall()
            
            for table in auto_tables:
                table_name = table[0]
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"   {table_name}: {count} rows")
        else:
            print("❌ auto_learning_items table does not exist")
            
            # Check what tables do exist
            print("\n📋 EXISTING TABLES:")
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            for table in tables:
                print(f"   {table[0]}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Check failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_table_structure()
