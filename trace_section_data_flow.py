#!/usr/bin/env python3
"""
CRITICAL INVESTIGATION: Section Data Flow Integrity
Trace exactly where section information is being lost between extraction and auto-learning
"""

import sys
import os
import sqlite3
import json
from datetime import datetime

def trace_section_data_flow():
    """Trace section information through the entire data pipeline"""
    print("🔍 CRITICAL INVESTIGATION: SECTION DATA FLOW INTEGRITY")
    print("=" * 70)
    
    try:
        # 1. Connect to database
        db_path = r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db"
        if not os.path.exists(db_path):
            print("❌ Database not found")
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get current session
        cursor.execute("SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1")
        result = cursor.fetchone()
        current_session = result[0] if result else None
        print(f"📋 Current session: {current_session}")
        
        if not current_session:
            print("❌ No current session available")
            return False
        
        # 2. CHECK EXTRACTION DATA (extracted_data table)
        print("\n2. 📊 CHECKING EXTRACTION DATA (extracted_data table):")
        
        cursor.execute("""
            SELECT section_name, item_label, COUNT(*) as frequency
            FROM extracted_data 
            WHERE session_id = ? AND period_type = 'current'
            GROUP BY section_name, item_label
            ORDER BY section_name, frequency DESC
            LIMIT 20
        """, (current_session,))
        
        extraction_data = cursor.fetchall()
        
        if extraction_data:
            print("   📋 Sample extraction data (section → item → frequency):")
            extraction_sections = {}
            for section, item, freq in extraction_data:
                if section not in extraction_sections:
                    extraction_sections[section] = []
                extraction_sections[section].append((item, freq))
                print(f"      {section}: {item} (appears {freq} times)")
            
            print(f"\n   📊 Extraction sections found: {list(extraction_sections.keys())}")
        else:
            print("   ❌ No extraction data found")
            return False
        
        # 3. CHECK AUTO-LEARNING DATA (auto_learning_results table)
        print("\n3. 📊 CHECKING AUTO-LEARNING DATA (auto_learning_results table):")
        
        cursor.execute("""
            SELECT section_name, item_label, confidence_score
            FROM auto_learning_results 
            WHERE session_id = ?
            ORDER BY section_name, item_label
            LIMIT 20
        """, (current_session,))
        
        auto_learning_data = cursor.fetchall()
        
        if auto_learning_data:
            print("   📋 Sample auto-learning data (section → item → confidence):")
            auto_learning_sections = {}
            for section, item, confidence in auto_learning_data:
                if section not in auto_learning_sections:
                    auto_learning_sections[section] = []
                auto_learning_sections[section].append((item, confidence))
                print(f"      {section}: {item} (confidence: {confidence})")
            
            print(f"\n   📊 Auto-learning sections found: {list(auto_learning_sections.keys())}")
        else:
            print("   ❌ No auto-learning data found")
            return False
        
        # 4. CROSS-REFERENCE SPECIFIC ITEMS
        print("\n4. 🔄 CROSS-REFERENCING SPECIFIC ITEMS:")
        
        # Get some items from extraction data
        test_items = [item[1] for item in extraction_data[:10]]  # Get first 10 item labels
        
        print(f"   🧪 Testing {len(test_items)} items from extraction data:")
        
        for test_item in test_items[:5]:  # Test first 5
            print(f"\n   🔍 Tracing item: '{test_item}'")
            
            # Check in extraction data
            cursor.execute("""
                SELECT section_name, COUNT(*) as frequency
                FROM extracted_data 
                WHERE session_id = ? AND item_label = ? AND period_type = 'current'
                GROUP BY section_name
                ORDER BY frequency DESC
            """, (current_session, test_item))
            
            extraction_sections = cursor.fetchall()
            
            if extraction_sections:
                print(f"      📊 Extraction: Found in {len(extraction_sections)} sections:")
                for section, freq in extraction_sections:
                    print(f"         {section}: {freq} occurrences")
                
                # Get the most frequent section (should be the correct one)
                primary_section = extraction_sections[0][0]
                print(f"      ✅ Primary section in extraction: {primary_section}")
            else:
                print("      ❌ Not found in extraction data")
                continue
            
            # Check in auto-learning data
            cursor.execute("""
                SELECT section_name, confidence_score
                FROM auto_learning_results 
                WHERE session_id = ? AND item_label = ?
            """, (current_session, test_item))
            
            auto_learning_sections = cursor.fetchall()
            
            if auto_learning_sections:
                print(f"      📊 Auto-learning: Found in {len(auto_learning_sections)} sections:")
                for section, confidence in auto_learning_sections:
                    print(f"         {section}: confidence {confidence}")
                
                auto_learning_section = auto_learning_sections[0][0]
                
                # Check if sections match
                if primary_section == auto_learning_section:
                    print(f"      ✅ SECTION MATCH: {primary_section}")
                else:
                    print(f"      ❌ SECTION MISMATCH: {primary_section} → {auto_learning_section}")
                    print(f"         🚨 CRITICAL: Section information was corrupted!")
            else:
                print("      ❌ Not found in auto-learning data")
        
        # 5. ANALYZE THE _analyze_for_new_items LOGIC
        print("\n5. 🔍 ANALYZING AUTO-LEARNING LOGIC:")
        
        # Check how items are being processed in the auto-learning phase
        # Look at the _analyze_for_new_items method logic
        
        print("   📋 Checking the auto-learning processing logic...")
        
        # Get the source code of the problematic method
        try:
            import inspect
            sys.path.append(os.path.join(os.path.dirname(__file__), 'core'))
            from phased_process_manager import PhasedProcessManager
            
            manager = PhasedProcessManager(debug_mode=True)
            
            # Check if we can access the method
            if hasattr(manager, '_analyze_for_new_items'):
                print("   ✅ Found _analyze_for_new_items method")
                
                # Let's simulate what this method does
                print("   🧪 Simulating auto-learning analysis...")
                
                # Get current data format
                cursor.execute("""
                    SELECT employee_id, section_name, item_label, item_value
                    FROM extracted_data 
                    WHERE session_id = ? AND period_type = 'current'
                    LIMIT 100
                """, (current_session,))
                
                sample_data = cursor.fetchall()
                
                if sample_data:
                    # Group by employee
                    employees = {}
                    for emp_id, section, item, value in sample_data:
                        if emp_id not in employees:
                            employees[emp_id] = {}
                        if section not in employees[emp_id]:
                            employees[emp_id][section] = {}
                        employees[emp_id][section][item] = value
                    
                    print(f"   📊 Sample data structure: {len(employees)} employees")
                    
                    # Show structure for first employee
                    first_emp = list(employees.keys())[0]
                    print(f"   📋 First employee ({first_emp}) structure:")
                    for section, items in employees[first_emp].items():
                        print(f"      {section}: {len(items)} items")
                        for item in list(items.keys())[:3]:  # Show first 3 items
                            print(f"         {item}")
                
            else:
                print("   ❌ Could not access _analyze_for_new_items method")
                
        except Exception as e:
            print(f"   ❌ Error analyzing auto-learning logic: {e}")
        
        # 6. CHECK THE ACTUAL PROBLEM
        print("\n6. 🎯 IDENTIFYING THE ROOT CAUSE:")
        
        # Check if there's a pattern in the section mismatches
        cursor.execute("""
            SELECT 
                e.section_name as extraction_section,
                a.section_name as auto_learning_section,
                e.item_label,
                COUNT(*) as frequency
            FROM extracted_data e
            JOIN auto_learning_results a ON (
                e.session_id = a.session_id AND 
                e.item_label = a.item_label
            )
            WHERE e.session_id = ? AND e.period_type = 'current'
            AND e.section_name != a.section_name
            GROUP BY e.section_name, a.section_name, e.item_label
            ORDER BY frequency DESC
            LIMIT 20
        """, (current_session,))
        
        mismatches = cursor.fetchall()
        
        if mismatches:
            print(f"   🚨 FOUND {len(mismatches)} SECTION MISMATCHES:")
            print("   📋 Extraction Section → Auto-Learning Section (Item, Frequency):")
            
            mismatch_patterns = {}
            for ext_section, auto_section, item, freq in mismatches:
                pattern = f"{ext_section} → {auto_section}"
                if pattern not in mismatch_patterns:
                    mismatch_patterns[pattern] = []
                mismatch_patterns[pattern].append((item, freq))
                print(f"      {ext_section} → {auto_section}: {item} ({freq} times)")
            
            print(f"\n   📊 Mismatch patterns:")
            for pattern, items in mismatch_patterns.items():
                total_items = len(items)
                print(f"      {pattern}: {total_items} items affected")
        else:
            print("   ✅ No section mismatches found - sections are preserved correctly")
        
        # 7. FINAL DIAGNOSIS
        print("\n7. 🎯 FINAL DIAGNOSIS:")
        
        if mismatches:
            print("   🚨 CRITICAL ISSUE CONFIRMED:")
            print("      • Section information IS being corrupted between extraction and auto-learning")
            print("      • The Perfect Section Aware Extractor is working correctly")
            print("      • The issue is in the phased_process_manager._analyze_for_new_items method")
            print("      • Items are being reassigned to wrong sections during auto-learning analysis")
            
            print("\n   🔧 REQUIRED FIX:")
            print("      • Preserve original section from extraction data")
            print("      • Do NOT recalculate or reassign sections in auto-learning")
            print("      • Use extraction section as the authoritative source")
        else:
            print("   ✅ SECTIONS ARE PRESERVED CORRECTLY:")
            print("      • No section corruption detected")
            print("      • The issue may be in confidence calculation only")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Investigation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = trace_section_data_flow()
    sys.exit(0 if success else 1)
