#!/usr/bin/env python3
"""
CRITICAL DATA RECOVERY: Investigate Current Database State
Examines the current state of dictionary data after migration and identifies what needs to be recovered
"""

import sqlite3
import os
import sys
import json
from datetime import datetime

def investigate_database_state():
    """Investigate the current state of all databases"""
    print("🔍 INVESTIGATING CURRENT DATABASE STATE")
    print("=" * 50)
    
    # Check all possible database locations
    database_paths = [
        'data/templar_payroll_auditor.db',
        'payroll_audit.db',
        'data/payroll_audit.db',
        'templar_payroll_auditor.db'
    ]
    
    for db_path in database_paths:
        if os.path.exists(db_path):
            print(f"\n📁 EXAMINING: {db_path}")
            print("-" * 40)
            
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # Check if dictionary tables exist
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'dictionary%'")
                dict_tables = cursor.fetchall()
                
                if dict_tables:
                    print(f"   📋 Dictionary tables found: {[table[0] for table in dict_tables]}")
                    
                    # Check dictionary_sections
                    try:
                        cursor.execute("SELECT COUNT(*) FROM dictionary_sections")
                        section_count = cursor.fetchone()[0]
                        print(f"   📊 Dictionary sections: {section_count}")
                        
                        if section_count > 0:
                            cursor.execute("SELECT section_name FROM dictionary_sections ORDER BY section_name")
                            sections = cursor.fetchall()
                            print("   📝 Sections found:")
                            for section in sections:
                                print(f"      - {section[0]}")
                    except Exception as e:
                        print(f"   ❌ Error checking sections: {e}")
                    
                    # Check dictionary_items
                    try:
                        cursor.execute("SELECT COUNT(*) FROM dictionary_items")
                        item_count = cursor.fetchone()[0]
                        print(f"   📊 Dictionary items: {item_count}")
                        
                        if item_count > 0:
                            cursor.execute("""
                                SELECT ds.section_name, di.item_name, di.created_at
                                FROM dictionary_sections ds 
                                JOIN dictionary_items di ON ds.id = di.section_id 
                                ORDER BY ds.section_name, di.item_name
                                LIMIT 20
                            """)
                            items = cursor.fetchall()
                            print("   📝 Items found (first 20):")
                            for section_name, item_name, created_at in items:
                                print(f"      - {section_name} -> {item_name} ({created_at})")
                    except Exception as e:
                        print(f"   ❌ Error checking items: {e}")
                    
                    # Check table structure for change detection columns
                    try:
                        cursor.execute("PRAGMA table_info(dictionary_items)")
                        columns = cursor.fetchall()
                        column_names = [col[1] for col in columns]
                        
                        change_detection_columns = [
                            'include_new', 'include_increase', 'include_decrease', 
                            'include_removed', 'include_no_change'
                        ]
                        
                        print("   🔧 Change detection columns:")
                        for col in change_detection_columns:
                            status = "✅ Present" if col in column_names else "❌ Missing"
                            print(f"      - {col}: {status}")
                    except Exception as e:
                        print(f"   ❌ Error checking columns: {e}")
                        
                else:
                    print("   ❌ No dictionary tables found")
                
                conn.close()
                
            except Exception as e:
                print(f"   ❌ Error accessing database: {e}")
        else:
            print(f"\n📁 NOT FOUND: {db_path}")

def check_for_backup_data():
    """Check for any backup data or previous versions"""
    print(f"\n💾 CHECKING FOR BACKUP DATA")
    print("-" * 30)
    
    # Check for backup files
    backup_patterns = [
        '*.db.backup',
        '*.db.bak',
        'backup_*.db',
        '*_backup.db'
    ]
    
    import glob
    
    backup_files = []
    for pattern in backup_patterns:
        backup_files.extend(glob.glob(pattern))
        backup_files.extend(glob.glob(f'data/{pattern}'))
    
    if backup_files:
        print("   📁 Backup files found:")
        for backup_file in backup_files:
            print(f"      - {backup_file}")
            
            # Check backup file contents
            try:
                conn = sqlite3.connect(backup_file)
                cursor = conn.cursor()
                
                cursor.execute("SELECT COUNT(*) FROM dictionary_sections")
                section_count = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM dictionary_items")
                item_count = cursor.fetchone()[0]
                
                print(f"        Sections: {section_count}, Items: {item_count}")
                
                if item_count > 10:
                    print(f"        ✅ POTENTIAL RECOVERY SOURCE")
                
                conn.close()
                
            except Exception as e:
                print(f"        ❌ Error checking backup: {e}")
    else:
        print("   ❌ No backup files found")

def analyze_data_loss_timeline():
    """Analyze when data loss might have occurred"""
    print(f"\n🕐 ANALYZING DATA LOSS TIMELINE")
    print("-" * 30)
    
    db_path = 'data/templar_payroll_auditor.db'
    if os.path.exists(db_path):
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Check for any remaining items with timestamps
            cursor.execute("""
                SELECT item_name, created_at, updated_at 
                FROM dictionary_items 
                ORDER BY COALESCE(updated_at, created_at) DESC 
                LIMIT 10
            """)
            
            recent_items = cursor.fetchall()
            
            if recent_items:
                print("   📊 Most recent dictionary activity:")
                for item_name, created_at, updated_at in recent_items:
                    last_activity = updated_at if updated_at else created_at
                    print(f"      - {item_name}: {last_activity}")
                
                # Determine if this looks like test data or real data
                test_indicators = ['TEST', 'MIGRATION', 'SAMPLE', 'DEMO']
                real_data_count = 0
                test_data_count = 0
                
                for item_name, _, _ in recent_items:
                    if any(indicator in item_name.upper() for indicator in test_indicators):
                        test_data_count += 1
                    else:
                        real_data_count += 1
                
                print(f"\n   📊 Data analysis:")
                print(f"      Real data items: {real_data_count}")
                print(f"      Test data items: {test_data_count}")
                
                if test_data_count > real_data_count:
                    print("   ⚠️ Mostly test data - original data likely lost")
                else:
                    print("   ✅ Some real data remains")
            else:
                print("   ❌ No dictionary items found")
            
            conn.close()
            
        except Exception as e:
            print(f"   ❌ Error analyzing timeline: {e}")

def create_recovery_plan():
    """Create a data recovery plan based on findings"""
    print(f"\n📋 DATA RECOVERY PLAN")
    print("-" * 30)
    
    print("   🎯 Recovery Strategy:")
    print("      1. Check for any remaining original data in current database")
    print("      2. Search for backup databases with original data")
    print("      3. Restore from most complete backup source available")
    print("      4. Recreate essential dictionary structure if no backups found")
    print("      5. Verify all change detection columns are properly configured")
    print("      6. Test Dictionary Manager functionality after recovery")
    
    print("\n   ⚠️ Critical Actions:")
    print("      - Do NOT run any save operations until data is recovered")
    print("      - Create backup of current state before any recovery attempts")
    print("      - Verify data integrity after each recovery step")

def main():
    """Main investigation function"""
    print("🚨 CRITICAL DATA RECOVERY INVESTIGATION")
    print("=" * 60)
    
    # Investigate current database state
    investigate_database_state()
    
    # Check for backup data
    check_for_backup_data()
    
    # Analyze data loss timeline
    analyze_data_loss_timeline()
    
    # Create recovery plan
    create_recovery_plan()
    
    print("\n" + "=" * 60)
    print("📊 INVESTIGATION COMPLETE")
    print("🎯 Next step: Execute data recovery based on findings above")

if __name__ == "__main__":
    main()
