#!/usr/bin/env python3
"""
Debug Item Lookup Issue
"""

import sys
import os
import sqlite3
import json

def debug_item_lookup():
    """Debug why item lookup is failing"""
    print("🔍 DEBUGGING ITEM LOOKUP ISSUE")
    print("=" * 40)
    
    try:
        # 1. Connect to database
        db_path = r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get current session
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.session_manager import get_current_session_id
            current_session = get_current_session_id()
            print(f"📋 Current session: {current_session}")
        except Exception as e:
            cursor.execute("SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1")
            result = cursor.fetchone()
            current_session = result[0] if result else None
            print(f"📋 Latest session from DB: {current_session}")
        
        # 2. Check if the test item exists
        test_item_id = 8582
        print(f"\n2. 🔍 CHECKING ITEM {test_item_id}:")
        
        # Check without session filter first
        cursor.execute("""
            SELECT id, session_id, section_name, item_label, auto_approved
            FROM auto_learning_results 
            WHERE id = ?
        """, (test_item_id,))
        
        item_without_session = cursor.fetchone()
        
        if item_without_session:
            print(f"   ✅ Item exists in database:")
            print(f"      ID: {item_without_session[0]}")
            print(f"      Session: {item_without_session[1]}")
            print(f"      Section: {item_without_session[2]}")
            print(f"      Label: {item_without_session[3]}")
            print(f"      Auto-approved: {item_without_session[4]}")
            
            # Check if session matches
            if item_without_session[1] == current_session:
                print("   ✅ Session matches current session")
            else:
                print(f"   ❌ Session mismatch: item session = {item_without_session[1]}, current = {current_session}")
        else:
            print("   ❌ Item not found in database")
        
        # 3. Check with session filter
        print(f"\n3. 🔍 CHECKING WITH SESSION FILTER:")
        
        cursor.execute("""
            SELECT id, section_name, item_label, auto_approved
            FROM auto_learning_results 
            WHERE id = ? AND session_id = ?
        """, (test_item_id, current_session))
        
        item_with_session = cursor.fetchone()
        
        if item_with_session:
            print(f"   ✅ Item found with session filter:")
            print(f"      ID: {item_with_session[0]}")
            print(f"      Section: {item_with_session[1]}")
            print(f"      Label: {item_with_session[2]}")
            print(f"      Auto-approved: {item_with_session[3]}")
        else:
            print("   ❌ Item not found with session filter")
        
        # 4. Check all items in current session
        print(f"\n4. 📊 ALL ITEMS IN CURRENT SESSION:")
        
        cursor.execute("""
            SELECT COUNT(*) 
            FROM auto_learning_results 
            WHERE session_id = ?
        """, (current_session,))
        
        total_items = cursor.fetchone()[0]
        print(f"   📊 Total items in session: {total_items}")
        
        # Show some sample items
        cursor.execute("""
            SELECT id, section_name, item_label, auto_approved
            FROM auto_learning_results 
            WHERE session_id = ?
            ORDER BY id
            LIMIT 10
        """, (current_session,))
        
        sample_items = cursor.fetchall()
        
        print("   📋 Sample items:")
        for item in sample_items:
            print(f"      ID {item[0]}: {item[2]} → {item[1]} (auto-approved: {item[3]})")
        
        # 5. Check pending items specifically
        print(f"\n5. 📋 PENDING ITEMS (auto_approved = 0):")
        
        cursor.execute("""
            SELECT id, section_name, item_label
            FROM auto_learning_results 
            WHERE session_id = ? AND auto_approved = 0
            ORDER BY id
            LIMIT 10
        """, (current_session,))
        
        pending_items = cursor.fetchall()
        
        print(f"   📊 Pending items count: {len(pending_items)}")
        
        if pending_items:
            print("   📋 Sample pending items:")
            for item in pending_items:
                print(f"      ID {item[0]}: {item[2]} → {item[1]}")
        
        # 6. Test the API function directly
        print(f"\n6. 🧪 TESTING API FUNCTION DIRECTLY:")
        
        # Import and test the API function
        sys.path.append(os.path.join(os.path.dirname(__file__), 'core'))
        
        try:
            from auto_learning_section_api import update_item_section, get_pending_auto_learning_items
            
            # Test get pending items
            pending_result = get_pending_auto_learning_items()
            print(f"   📊 Get pending items result: {pending_result}")
            
            # Test with a known pending item
            if pending_items:
                test_id = pending_items[0][0]
                print(f"   🧪 Testing update with item ID {test_id}")
                
                update_result = update_item_section(test_id, "EARNINGS")
                print(f"   📊 Update result: {update_result}")
            
        except Exception as e:
            print(f"   ❌ API function test failed: {e}")
        
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_item_lookup()
