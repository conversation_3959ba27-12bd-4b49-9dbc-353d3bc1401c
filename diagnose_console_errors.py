#!/usr/bin/env python3
"""
Diagnose Console <PERSON>rrors - Root Cause Analysis
"""

import sys
import os
import sqlite3
import json

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def diagnose_console_errors():
    """Diagnose the console errors"""
    print("🔍 DIAGNOSING CONSOLE ERRORS")
    print("=" * 50)
    
    try:
        # 1. Check database connectivity
        db_path = get_database_path()
        if not db_path:
            print("❌ Database not found")
            return
        
        print(f"✅ Database found: {db_path}")
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 2. Check current session
        print("\n2. 📋 CHECKING CURRENT SESSION:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.session_manager import get_current_session_id
            current_session = get_current_session_id()
            print(f"   Current session: {current_session}")
        except Exception as e:
            print(f"   ❌ Session manager error: {e}")
            # Get latest session from database
            cursor.execute("SELECT DISTINCT session_id FROM comparison_results ORDER BY session_id DESC LIMIT 1")
            result = cursor.fetchone()
            current_session = result[0] if result else None
            print(f"   Latest session from DB: {current_session}")
        
        # 3. Check pre-reporting data
        print("\n3. 📊 CHECKING PRE-REPORTING DATA:")
        
        # Check comparison_results table
        cursor.execute("SELECT COUNT(*) FROM comparison_results")
        total_comparison = cursor.fetchone()[0]
        print(f"   Total comparison results: {total_comparison}")
        
        if current_session:
            cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (current_session,))
            session_comparison = cursor.fetchone()[0]
            print(f"   Current session comparison results: {session_comparison}")
            
            if session_comparison == 0:
                print("   ❌ NO COMPARISON DATA FOR CURRENT SESSION")
                print("   💡 This explains the pre-reporting data error")
        
        # 4. Check tracker data
        print("\n4. 🏦 CHECKING TRACKER DATA:")
        
        # Check tracker_results table
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='tracker_results'")
        tracker_table_exists = cursor.fetchone()
        
        if tracker_table_exists:
            cursor.execute("SELECT COUNT(*) FROM tracker_results")
            total_tracker = cursor.fetchone()[0]
            print(f"   Total tracker results: {total_tracker}")
            
            if current_session:
                cursor.execute("SELECT COUNT(*) FROM tracker_results WHERE session_id = ?", (current_session,))
                session_tracker = cursor.fetchone()[0]
                print(f"   Current session tracker results: {session_tracker}")
                
                if session_tracker == 0:
                    print("   ❌ NO TRACKER DATA FOR CURRENT SESSION")
                    print("   💡 This explains the tracker population error")
                else:
                    # Check tracker types
                    cursor.execute("""
                        SELECT tracker_type, COUNT(*) 
                        FROM tracker_results 
                        WHERE session_id = ? 
                        GROUP BY tracker_type
                    """, (current_session,))
                    
                    tracker_types = cursor.fetchall()
                    print("   📋 Tracker types in current session:")
                    for tracker_type, count in tracker_types:
                        print(f"      {tracker_type}: {count} items")
        else:
            print("   ❌ tracker_results table does not exist")
        
        # 5. Test the specific commands that are failing
        print("\n5. 🧪 TESTING FAILING COMMANDS:")
        
        # Test get-latest-pre-reporting-data
        print("   🔄 Testing get-latest-pre-reporting-data...")
        
        try:
            from core.phased_process_manager import PhasedProcessManager
            
            manager = PhasedProcessManager(debug_mode=True)
            result = manager.get_pre_reporting_data()
            
            print(f"   📊 Pre-reporting result: {result}")
            
            if not result or not result.get('success'):
                print("   ❌ Pre-reporting command failed")
                print(f"   🔍 Error: {result.get('error') if result else 'No result'}")
            else:
                print("   ✅ Pre-reporting command succeeded")
                
        except Exception as e:
            print(f"   ❌ Pre-reporting test failed: {e}")
        
        # Test populate_tables
        print("\n   🔄 Testing populate_tables...")
        
        try:
            import bank_adviser_tracker_operations
            
            # Simulate the command
            original_argv = sys.argv
            sys.argv = ['bank_adviser_tracker_operations.py', 'populate_tables']
            
            result = bank_adviser_tracker_operations.populate_bank_adviser_tables()
            
            sys.argv = original_argv
            
            print(f"   📊 Tracker population result: {result}")
            
            if not result or not result.get('success'):
                print("   ❌ Tracker population command failed")
                print(f"   🔍 Error: {result.get('error') if result else 'No result'}")
            else:
                print("   ✅ Tracker population command succeeded")
                
        except Exception as e:
            print(f"   ❌ Tracker population test failed: {e}")
        
        # 6. Check Bank Adviser tables
        print("\n6. 🏦 CHECKING BANK ADVISER TABLES:")
        
        bank_tables = ['in_house_loans', 'external_loans', 'motor_vehicle_maintenance']
        
        for table in bank_tables:
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
            table_exists = cursor.fetchone()
            
            if table_exists:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"   {table}: {count} records")
            else:
                print(f"   ❌ {table}: Table does not exist")
        
        # 7. Root cause analysis
        print("\n7. 🎯 ROOT CAUSE ANALYSIS:")
        print("   " + "=" * 40)
        
        issues_found = []
        
        if current_session is None:
            issues_found.append("No current session available")
        
        if total_comparison == 0:
            issues_found.append("No comparison data in database")
        elif current_session and session_comparison == 0:
            issues_found.append("No comparison data for current session")
        
        if not tracker_table_exists:
            issues_found.append("tracker_results table missing")
        elif total_tracker == 0:
            issues_found.append("No tracker data in database")
        elif current_session and session_tracker == 0:
            issues_found.append("No tracker data for current session")
        
        if issues_found:
            print("   🔍 ISSUES IDENTIFIED:")
            for issue in issues_found:
                print(f"      • {issue}")
            
            print("\n   💡 SOLUTIONS NEEDED:")
            if "No comparison data" in str(issues_found):
                print("      • Run a complete processing job to generate comparison data")
            if "No tracker data" in str(issues_found):
                print("      • Ensure tracker learning phase runs during processing")
            if "No current session" in str(issues_found):
                print("      • Fix session management system")
        else:
            print("   ✅ No obvious data issues found")
            print("   🔍 Issue may be in command execution or output formatting")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Diagnosis failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    diagnose_console_errors()
