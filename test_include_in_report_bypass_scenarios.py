#!/usr/bin/env python3
"""
Test INCLUDE IN REPORT Toggle Bypass Scenarios
Identify potential failure points and bypass scenarios
"""

import sys
import os
import sqlite3
import json

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def test_include_in_report_bypass_scenarios():
    """Test for potential bypass scenarios and failure points"""
    print("🔍 TESTING INCLUDE IN REPORT BYPASS SCENARIOS")
    print("=" * 60)
    
    bypass_tests = {
        'direct_database_bypass': False,
        'api_inconsistency_bypass': False,
        'report_generation_bypass': False,
        'comparison_phase_bypass': False,
        'extraction_phase_bypass': False,
        'ui_state_bypass': False,
        'session_isolation_bypass': False
    }
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return bypass_tests
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Direct Database Bypass Test
        print("\n1. 🗄️ DIRECT DATABASE BYPASS TEST:")
        
        try:
            # Test if data can be retrieved without checking include_in_report
            cursor.execute("SELECT COUNT(*) FROM dictionary_items")
            total_items = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM dictionary_items WHERE include_in_report = 1")
            included_items = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM dictionary_items WHERE include_in_report = 0")
            excluded_items = cursor.fetchone()[0]
            
            print(f"   Total items: {total_items}")
            print(f"   Included items: {included_items}")
            print(f"   Excluded items: {excluded_items}")
            
            # Test: Can we get excluded items directly?
            cursor.execute("SELECT item_name FROM dictionary_items WHERE include_in_report = 0 LIMIT 5")
            excluded_sample = cursor.fetchall()
            
            if excluded_items > 0:
                print(f"   ⚠️ BYPASS RISK: {excluded_items} excluded items can be accessed directly")
                print("   Sample excluded items:")
                for item in excluded_sample:
                    print(f"     - {item[0]}")
            else:
                print("   ✅ NO BYPASS: All items are included")
                bypass_tests['direct_database_bypass'] = True
                
        except Exception as e:
            print(f"   ❌ Direct database bypass test failed: {e}")
        
        # 2. API Inconsistency Bypass Test
        print("\n2. 🔄 API INCONSISTENCY BYPASS TEST:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.dictionary_manager import PayrollDictionaryManager
            from core.dictionary_integration import should_include_in_report
            
            manager = PayrollDictionaryManager(debug=False)
            
            # Create a test item that should be excluded
            test_section = "TEST_SECTION"
            test_item = "TEST_EXCLUDED_ITEM"
            
            # Test if different APIs give consistent results
            try:
                result1 = manager.should_include_in_report(test_section, test_item)
                result2 = should_include_in_report(test_section, test_item)
                
                if result1 == result2:
                    print(f"   ✅ API consistency: Both APIs return {result1}")
                    bypass_tests['api_inconsistency_bypass'] = True
                else:
                    print(f"   ⚠️ BYPASS RISK: API inconsistency ({result1} vs {result2})")
                    
            except Exception as e:
                print(f"   ❌ API consistency test failed: {e}")
                
        except Exception as e:
            print(f"   ❌ API inconsistency bypass test failed: {e}")
        
        # 3. Report Generation Bypass Test
        print("\n3. 📄 REPORT GENERATION BYPASS TEST:")
        
        try:
            # Check if report generation systems use include_in_report filtering
            report_systems = [
                'core/advanced_reporting_system.py',
                'core/phased_process_manager.py'
            ]
            
            bypass_risk_found = False
            
            for system_file in report_systems:
                if os.path.exists(system_file):
                    with open(system_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    if 'include_in_report' in content or 'includeInReport' in content:
                        print(f"   ✅ {system_file}: Uses include_in_report filtering")
                    else:
                        print(f"   ⚠️ {system_file}: BYPASS RISK - No include_in_report filtering")
                        bypass_risk_found = True
                else:
                    print(f"   ❌ {system_file}: File not found")
            
            if not bypass_risk_found:
                bypass_tests['report_generation_bypass'] = True
            
        except Exception as e:
            print(f"   ❌ Report generation bypass test failed: {e}")
        
        # 4. Comparison Phase Bypass Test
        print("\n4. ⚖️ COMPARISON PHASE BYPASS TEST:")
        
        try:
            # Check if comparison results respect include_in_report
            sys.path.append(os.path.dirname(__file__))
            from core.session_manager import get_current_session_id
            
            current_session = get_current_session_id()
            
            cursor.execute("""
                SELECT COUNT(*) FROM comparison_results 
                WHERE session_id = ?
            """, (current_session,))
            
            total_comparisons = cursor.fetchone()[0]
            
            if total_comparisons > 0:
                print(f"   Found {total_comparisons} comparison results")
                
                # Check if comparison results include items that should be excluded
                cursor.execute("""
                    SELECT DISTINCT cr.section_name, cr.item_label
                    FROM comparison_results cr
                    JOIN dictionary_items di ON cr.item_label = di.item_name
                    WHERE cr.session_id = ? AND di.include_in_report = 0
                    LIMIT 5
                """, (current_session,))
                
                excluded_in_comparison = cursor.fetchall()
                
                if excluded_in_comparison:
                    print(f"   ⚠️ BYPASS RISK: {len(excluded_in_comparison)} excluded items in comparison results")
                    for section, item in excluded_in_comparison:
                        print(f"     - {section}.{item}")
                else:
                    print("   ✅ NO BYPASS: No excluded items in comparison results")
                    bypass_tests['comparison_phase_bypass'] = True
            else:
                print("   ⚠️ No comparison results to test")
                bypass_tests['comparison_phase_bypass'] = True  # No data to bypass
                
        except Exception as e:
            print(f"   ❌ Comparison phase bypass test failed: {e}")
        
        # 5. Extraction Phase Bypass Test
        print("\n5. 📤 EXTRACTION PHASE BYPASS TEST:")
        
        try:
            # Check if extraction phase respects include_in_report
            cursor.execute("""
                SELECT COUNT(*) FROM extracted_data 
                WHERE session_id = ?
            """, (current_session,))
            
            total_extracted = cursor.fetchone()[0]
            
            if total_extracted > 0:
                print(f"   Found {total_extracted} extracted data records")
                
                # Check if extracted data includes items that should be excluded
                cursor.execute("""
                    SELECT DISTINCT ed.section_name, ed.item_label
                    FROM extracted_data ed
                    JOIN dictionary_items di ON ed.item_label = di.item_name
                    WHERE ed.session_id = ? AND di.include_in_report = 0
                    LIMIT 5
                """, (current_session,))
                
                excluded_in_extraction = cursor.fetchall()
                
                if excluded_in_extraction:
                    print(f"   ⚠️ BYPASS RISK: {len(excluded_in_extraction)} excluded items in extracted data")
                    for section, item in excluded_in_extraction:
                        print(f"     - {section}.{item}")
                else:
                    print("   ✅ NO BYPASS: No excluded items in extracted data")
                    bypass_tests['extraction_phase_bypass'] = True
            else:
                print("   ⚠️ No extracted data to test")
                bypass_tests['extraction_phase_bypass'] = True  # No data to bypass
                
        except Exception as e:
            print(f"   ❌ Extraction phase bypass test failed: {e}")
        
        # 6. UI State Bypass Test
        print("\n6. 🖥️ UI STATE BYPASS TEST:")
        
        try:
            # Check if UI can be manipulated to bypass toggle
            ui_files = ['renderer.js', 'index.html']
            
            ui_protection_found = False
            
            for ui_file in ui_files:
                if os.path.exists(ui_file):
                    with open(ui_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Look for toggle validation or protection
                    if 'include-toggle' in content and ('checked' in content or 'validation' in content):
                        print(f"   ✅ {ui_file}: Toggle validation found")
                        ui_protection_found = True
                    else:
                        print(f"   ⚠️ {ui_file}: Limited toggle protection")
                else:
                    print(f"   ❌ {ui_file}: File not found")
            
            if ui_protection_found:
                bypass_tests['ui_state_bypass'] = True
            
        except Exception as e:
            print(f"   ❌ UI state bypass test failed: {e}")
        
        # 7. Session Isolation Bypass Test
        print("\n7. 🔒 SESSION ISOLATION BYPASS TEST:")
        
        try:
            # Test if include_in_report settings are properly isolated by session
            cursor.execute("""
                SELECT COUNT(DISTINCT session_id) FROM extracted_data
            """)
            
            total_sessions = cursor.fetchone()[0]
            
            if total_sessions > 1:
                print(f"   Found {total_sessions} different sessions")
                
                # Check if include_in_report is consistent across sessions
                cursor.execute("""
                    SELECT item_name, include_in_report, COUNT(*) as count
                    FROM dictionary_items
                    GROUP BY item_name, include_in_report
                    HAVING COUNT(*) > 1
                """)
                
                inconsistent_items = cursor.fetchall()
                
                if inconsistent_items:
                    print(f"   ⚠️ BYPASS RISK: {len(inconsistent_items)} items with inconsistent include_in_report")
                    for item, include, count in inconsistent_items:
                        print(f"     - {item}: {count} different settings")
                else:
                    print("   ✅ NO BYPASS: Consistent include_in_report across sessions")
                    bypass_tests['session_isolation_bypass'] = True
            else:
                print("   ✅ Single session: No isolation issues")
                bypass_tests['session_isolation_bypass'] = True
                
        except Exception as e:
            print(f"   ❌ Session isolation bypass test failed: {e}")
        
        # 8. Summary and Recommendations
        print("\n8. 📋 BYPASS TEST SUMMARY:")
        print("   " + "=" * 50)
        
        secure_tests = sum(bypass_tests.values())
        total_tests = len(bypass_tests)
        
        for test_name, result in bypass_tests.items():
            status = "✅ SECURE" if result else "⚠️ BYPASS RISK"
            print(f"   {test_name.replace('_', ' ').title()}: {status}")
        
        print("   " + "=" * 50)
        print(f"   SECURITY RESULT: {secure_tests}/{total_tests} tests secure")
        
        if secure_tests == total_tests:
            print("   🎯 INCLUDE IN REPORT TOGGLE: FULLY SECURE ✅")
            print("   🔒 NO BYPASS RISKS: All scenarios protected")
        elif secure_tests >= total_tests * 0.8:
            print("   ⚠️ INCLUDE IN REPORT TOGGLE: MOSTLY SECURE")
            print("   🔧 MINOR RISKS: Some bypass scenarios need attention")
        else:
            print("   ❌ INCLUDE IN REPORT TOGGLE: SECURITY ISSUES")
            print("   🚨 CRITICAL RISKS: Multiple bypass scenarios found")
        
        # 9. Recommendations
        print("\n9. 💡 RECOMMENDATIONS:")
        
        if not bypass_tests['report_generation_bypass']:
            print("   🔧 Implement include_in_report filtering in backend report generation")
        
        if not bypass_tests['comparison_phase_bypass']:
            print("   🔧 Add include_in_report filtering to comparison phase")
        
        if not bypass_tests['extraction_phase_bypass']:
            print("   🔧 Consider include_in_report filtering during extraction")
        
        if secure_tests == total_tests:
            print("   ✅ No immediate action required - system is secure")
        
        conn.close()
        return bypass_tests
        
    except Exception as e:
        print(f"❌ Critical error during bypass testing: {e}")
        import traceback
        traceback.print_exc()
        return bypass_tests

if __name__ == "__main__":
    results = test_include_in_report_bypass_scenarios()
    
    # Exit with appropriate code
    secure_tests = sum(results.values())
    total_tests = len(results)
    
    if secure_tests >= total_tests * 0.8:  # 80% secure threshold
        sys.exit(0)  # Acceptable security level
    else:
        sys.exit(1)  # Security issues found
