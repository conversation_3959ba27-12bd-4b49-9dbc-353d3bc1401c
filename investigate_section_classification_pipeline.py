#!/usr/bin/env python3
"""
Investigate Section Classification Pipeline
Root cause analysis for section misclassification issues
"""

import sys
import os
import sqlite3
import json

def investigate_section_classification():
    """Investigate the section classification pipeline"""
    print("🔍 INVESTIGATING SECTION CLASSIFICATION PIPELINE")
    print("=" * 60)
    
    try:
        # 1. Check database connectivity
        db_path = r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db"
        if not os.path.exists(db_path):
            print("❌ Database not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get current session
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.session_manager import get_current_session_id
            current_session = get_current_session_id()
            print(f"📋 Current session: {current_session}")
        except Exception as e:
            print(f"❌ Session manager error: {e}")
            cursor.execute("SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1")
            result = cursor.fetchone()
            current_session = result[0] if result else None
            print(f"📋 Latest session from DB: {current_session}")
        
        if not current_session:
            print("❌ No current session available")
            return
        
        # 2. Analyze auto_learning_results data
        print("\n2. 📊 ANALYZING AUTO-LEARNING RESULTS DATA:")
        
        cursor.execute("""
            SELECT section_name, item_label, confidence_score, id
            FROM auto_learning_results 
            WHERE session_id = ?
            ORDER BY item_label
            LIMIT 20
        """, (current_session,))
        
        auto_learning_items = cursor.fetchall()
        
        print(f"   📊 Found {len(auto_learning_items)} auto-learning items")
        print("   📋 Sample items from auto_learning_results:")
        
        section_distribution = {}
        for section_name, item_label, confidence, item_id in auto_learning_items:
            print(f"      ID {item_id}: {item_label} → {section_name} (confidence: {confidence})")
            section_distribution[section_name] = section_distribution.get(section_name, 0) + 1
        
        print(f"\n   📊 Section distribution in auto_learning_results:")
        for section, count in section_distribution.items():
            print(f"      {section}: {count} items")
        
        # 3. Check comparison_results for the same items
        print("\n3. 📊 ANALYZING COMPARISON RESULTS DATA:")

        # Get some item labels to trace through the pipeline
        sample_items = [item[1] for item in auto_learning_items[:10]]  # item_label

        if sample_items:
            placeholders = ','.join(['?' for _ in sample_items])
            cursor.execute(f"""
                SELECT item_label, section_name, change_type, id
                FROM comparison_results
                WHERE session_id = ? AND item_label IN ({placeholders})
                ORDER BY item_label
            """, [current_session] + sample_items)

            comparison_items = cursor.fetchall()

            print(f"   📊 Found {len(comparison_items)} matching items in comparison_results")
            print("   📋 Sample items from comparison_results:")

            comparison_sections = {}
            for item_label, section_name, change_type, item_id in comparison_items:
                print(f"      ID {item_id}: {item_label} → {section_name} ({change_type})")
                comparison_sections[section_name] = comparison_sections.get(section_name, 0) + 1

            print(f"\n   📊 Section distribution in comparison_results:")
            for section, count in comparison_sections.items():
                print(f"      {section}: {count} items")
        
        # 4. Check extraction_results for original section detection
        print("\n4. 📊 ANALYZING EXTRACTION RESULTS DATA:")

        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='extraction_results'")
        if cursor.fetchone():
            if sample_items:
                cursor.execute(f"""
                    SELECT item_label, section_name, confidence_score, id
                    FROM extraction_results
                    WHERE session_id = ? AND item_label IN ({placeholders})
                    ORDER BY item_label
                """, [current_session] + sample_items)

                extraction_items = cursor.fetchall()

                print(f"   📊 Found {len(extraction_items)} matching items in extraction_results")
                print("   📋 Sample items from extraction_results:")

                extraction_sections = {}
                for item_label, section_name, confidence, item_id in extraction_items:
                    print(f"      ID {item_id}: {item_label} → {section_name} (confidence: {confidence})")
                    extraction_sections[section_name] = extraction_sections.get(section_name, 0) + 1

                print(f"\n   📊 Section distribution in extraction_results:")
                for section, count in extraction_sections.items():
                    print(f"      {section}: {count} items")
        else:
            print("   ❌ extraction_results table not found")
        
        # 5. Cross-reference the same items across tables
        print("\n5. 🔄 CROSS-REFERENCING ITEMS ACROSS PIPELINE:")
        
        if sample_items and len(sample_items) >= 3:
            test_items = sample_items[:3]  # Test first 3 items
            
            for test_item in test_items:
                print(f"\n   🔍 Tracing item: '{test_item}'")
                
                # Check in extraction_results
                cursor.execute("""
                    SELECT section_name, confidence_score
                    FROM extraction_results
                    WHERE session_id = ? AND item_label = ?
                """, (current_session, test_item))

                extraction_result = cursor.fetchone()
                if extraction_result:
                    print(f"      Extraction: {extraction_result[0]} (confidence: {extraction_result[1]})")
                else:
                    print("      Extraction: Not found")

                # Check in comparison_results
                cursor.execute("""
                    SELECT section_name, change_type
                    FROM comparison_results
                    WHERE session_id = ? AND item_label = ?
                """, (current_session, test_item))

                comparison_result = cursor.fetchone()
                if comparison_result:
                    print(f"      Comparison: {comparison_result[0]} ({comparison_result[1]})")
                else:
                    print("      Comparison: Not found")
                
                # Check in auto_learning_results
                cursor.execute("""
                    SELECT section_name, confidence_score 
                    FROM auto_learning_results 
                    WHERE session_id = ? AND item_label = ?
                """, (current_session, test_item))
                
                auto_learning_result = cursor.fetchone()
                if auto_learning_result:
                    print(f"      Auto-Learning: {auto_learning_result[0]} (confidence: {auto_learning_result[1]})")
                else:
                    print("      Auto-Learning: Not found")
        
        # 6. Check for section mapping or transformation logic
        print("\n6. 🔍 CHECKING FOR SECTION TRANSFORMATION LOGIC:")
        
        # Check if there's a pattern in the misclassification
        cursor.execute("""
            SELECT DISTINCT section_name, COUNT(*) as count
            FROM auto_learning_results 
            WHERE session_id = ?
            GROUP BY section_name
            ORDER BY count DESC
        """, (current_session,))
        
        all_sections = cursor.fetchall()
        
        print("   📊 All sections in auto_learning_results:")
        for section, count in all_sections:
            print(f"      {section}: {count} items")
        
        # Check if most items are going to "Personal Details"
        personal_details_count = next((count for section, count in all_sections if section == "PERSONAL DETAILS"), 0)
        total_items = sum(count for _, count in all_sections)
        
        if personal_details_count > total_items * 0.5:  # More than 50% are Personal Details
            print(f"\n   ⚠️ ISSUE DETECTED: {personal_details_count}/{total_items} ({personal_details_count/total_items*100:.1f}%) items classified as 'PERSONAL DETAILS'")
            print("   🔍 This suggests a default classification issue")
        
        # 7. Check the Auto-Learning system configuration
        print("\n7. 🔧 CHECKING AUTO-LEARNING SYSTEM CONFIGURATION:")
        
        try:
            from backup_created_files.enhanced_dictionary_auto_learning import EnhancedDictionaryAutoLearning
            
            auto_learning = EnhancedDictionaryAutoLearning(debug=True)
            
            # Check if there's a default section setting
            print("   📋 Auto-Learning system initialized successfully")
            
            # Get a sample of pending items to see their structure
            pending_items = auto_learning.get_pending_items()
            
            if pending_items:
                print(f"   📊 Pending items structure (first item):")
                first_item = pending_items[0]
                for key, value in first_item.items():
                    print(f"      {key}: {value}")
        
        except Exception as e:
            print(f"   ❌ Auto-Learning system check failed: {e}")
        
        conn.close()
        
        # 8. Root cause analysis
        print("\n8. 🎯 ROOT CAUSE ANALYSIS:")
        print("   " + "=" * 50)
        
        if personal_details_count > total_items * 0.5:
            print("   🔍 LIKELY ISSUE: Default section classification")
            print("   💡 HYPOTHESIS: Items are being assigned a default 'PERSONAL DETAILS' section")
            print("   🔧 SOLUTION NEEDED: Fix section detection/preservation in the pipeline")
        else:
            print("   🔍 Section distribution appears more balanced")
            print("   💡 HYPOTHESIS: Specific items are being misclassified")
            print("   🔧 SOLUTION NEEDED: Improve section detection accuracy")
        
        print("\n   📋 INVESTIGATION SUMMARY:")
        print("   • Check extraction → comparison → auto-learning data flow")
        print("   • Verify section preservation across pipeline stages")
        print("   • Identify where section information is being lost or overridden")
        
    except Exception as e:
        print(f"❌ Investigation failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    investigate_section_classification()
