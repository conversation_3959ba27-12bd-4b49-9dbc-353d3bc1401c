#!/usr/bin/env python3
"""
CRITICAL FIX: Dictionary Manager Save Operation
Fixes the critical issue where Dictionary Manager save operations fail with auto-save errors.
"""

import sqlite3
import os
import sys
import json
import tempfile
from datetime import datetime

def get_database_path():
    """Get the correct database path"""
    possible_paths = [
        'payroll_audit.db',
        'data/templar_payroll_auditor.db',
        'data/payroll_audit.db',
        'templar_payroll_auditor.db'
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    
    return 'payroll_audit.db'  # Default

def test_dictionary_save_operation():
    """Test the dictionary save operation to identify issues"""
    print("🔧 TESTING DICTIONARY SAVE OPERATION")
    print("=" * 50)
    
    db_path = get_database_path()
    print(f"📁 Database: {db_path}")
    
    if not os.path.exists(db_path):
        print(f"❌ Database not found at: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Test 1: Check if dictionary tables exist
        print("\n1. 📊 CHECKING DICTIONARY TABLES:")
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%dictionary%'")
        dict_tables = cursor.fetchall()
        
        if dict_tables:
            for table in dict_tables:
                print(f"   ✅ Found table: {table[0]}")
                
                # Check table structure
                cursor.execute(f"PRAGMA table_info({table[0]})")
                columns = cursor.fetchall()
                print(f"      Columns: {len(columns)}")
                
                # Check for change detection columns
                if table[0] == 'dictionary_items':
                    change_columns = ['include_new', 'include_increase', 'include_decrease', 'include_removed', 'include_no_change']
                    existing_columns = [col[1] for col in columns]
                    missing_columns = [col for col in change_columns if col not in existing_columns]
                    
                    if missing_columns:
                        print(f"      ❌ Missing change detection columns: {missing_columns}")
                        return False
                    else:
                        print(f"      ✅ All change detection columns present")
        else:
            print("   ❌ No dictionary tables found")
            return False
        
        # Test 2: Try a simple dictionary save operation
        print("\n2. 🧪 TESTING DICTIONARY SAVE OPERATION:")
        
        # Create test data
        test_dictionary = {
            "TEST_SECTION": {
                "items": {
                    "TEST_ITEM": {
                        "format": "text",
                        "value_format": "text",
                        "include_in_report": True,
                        "include_new": True,
                        "include_increase": True,
                        "include_decrease": True,
                        "include_removed": True,
                        "include_no_change": False,
                        "standard_key": "TEST_ITEM",
                        "standardized_name": "TEST_ITEM",
                        "is_fixed": False,
                        "variations": ["text"],
                        "validation_rules": {}
                    }
                }
            }
        }
        
        # Test the exact save logic from unified_database.js
        try:
            # Clear existing test data
            cursor.execute("DELETE FROM dictionary_items WHERE item_name = 'TEST_ITEM'")
            cursor.execute("DELETE FROM dictionary_sections WHERE section_name = 'TEST_SECTION'")
            
            # Insert test section
            cursor.execute("INSERT INTO dictionary_sections (section_name) VALUES (?)", ("TEST_SECTION",))
            cursor.execute("SELECT id FROM dictionary_sections WHERE section_name = ?", ("TEST_SECTION",))
            section_id = cursor.fetchone()[0]
            
            # Test the INSERT statement from our fix
            cursor.execute("""
                INSERT OR REPLACE INTO dictionary_items
                (section_id, item_name, standard_key, format_type, value_format,
                 include_in_report, include_new, include_increase, include_decrease, 
                 include_removed, include_no_change, is_fixed, validation_rules)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                section_id, "TEST_ITEM", "TEST_ITEM", "text", "text",
                1, 1, 1, 1, 1, 0, 0, "{}"
            ))
            
            conn.commit()
            print("   ✅ Dictionary save operation successful")
            
            # Verify the save
            cursor.execute("""
                SELECT include_new, include_increase, include_decrease, include_removed, include_no_change
                FROM dictionary_items WHERE item_name = 'TEST_ITEM'
            """)
            
            result = cursor.fetchone()
            if result == (1, 1, 1, 1, 0):
                print("   ✅ Change detection columns saved correctly")
            else:
                print(f"   ❌ Change detection columns incorrect: {result}")
                return False
            
            # Clean up test data
            cursor.execute("DELETE FROM dictionary_items WHERE item_name = 'TEST_ITEM'")
            cursor.execute("DELETE FROM dictionary_sections WHERE section_name = 'TEST_SECTION'")
            conn.commit()
            
        except Exception as e:
            print(f"   ❌ Dictionary save test failed: {e}")
            return False
        
        # Test 3: Check for potential issues with large data
        print("\n3. 📏 CHECKING FOR LARGE DATA ISSUES:")
        
        cursor.execute("SELECT COUNT(*) FROM dictionary_items")
        item_count = cursor.fetchone()[0]
        print(f"   📊 Current dictionary items: {item_count}")
        
        cursor.execute("SELECT COUNT(*) FROM dictionary_sections")
        section_count = cursor.fetchone()[0]
        print(f"   📊 Current dictionary sections: {section_count}")
        
        if item_count > 1000:
            print("   ⚠️ Large number of items - may cause performance issues")
        
        conn.close()
        
        print("\n✅ DICTIONARY SAVE OPERATION TEST COMPLETED SUCCESSFULLY")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_improved_fallback_mechanism():
    """Create an improved fallback mechanism that doesn't use command line arguments"""
    print("\n🔧 CREATING IMPROVED FALLBACK MECHANISM")
    print("=" * 50)
    
    # Create a temporary file-based fallback script
    fallback_script = '''#!/usr/bin/env python3
"""
Improved Dictionary Save Fallback
Uses file-based communication instead of command line arguments
"""

import sys
import json
import tempfile
import os

def save_dictionary_from_file(temp_file_path):
    """Save dictionary from temporary file"""
    try:
        # Read dictionary from temporary file
        with open(temp_file_path, 'r', encoding='utf-8') as f:
            dictionary = json.load(f)
        
        # Import the dictionary manager
        sys.path.append(os.path.dirname(__file__))
        from dictionary_manager import PayrollDictionaryManager
        
        # Create manager and save
        manager = PayrollDictionaryManager(debug=True)
        manager.dictionary = dictionary
        success = manager.save_dictionary()
        
        # Clean up temporary file
        os.unlink(temp_file_path)
        
        return success
        
    except Exception as e:
        print(f"Error in fallback save: {e}", file=sys.stderr)
        return False

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python dictionary_save_fallback.py <temp_file_path>")
        sys.exit(1)
    
    temp_file_path = sys.argv[1]
    success = save_dictionary_from_file(temp_file_path)
    print("true" if success else "false")
'''
    
    try:
        with open('core/dictionary_save_fallback.py', 'w', encoding='utf-8') as f:
            f.write(fallback_script)
        
        print("   ✅ Created improved fallback script: core/dictionary_save_fallback.py")
        return True
        
    except Exception as e:
        print(f"   ❌ Failed to create fallback script: {e}")
        return False

def create_main_js_fix():
    """Create the fix for main.js to use file-based fallback"""
    print("\n🔧 CREATING MAIN.JS FIX")
    print("=" * 50)
    
    fix_code = '''
// PRODUCTION FIX: Replace the problematic fallback in main.js around line 1717-1721
// Replace this code:
//   const result = await runHybridScript(pythonPath, ['save-dictionary', JSON.stringify(dictionary)]);

// With this improved version:
try {
  console.log('[DICTIONARY] Using improved file-based fallback...');
  
  // Write dictionary to temporary file
  const tempFile = path.join(os.tmpdir(), `dictionary_${Date.now()}.json`);
  fs.writeFileSync(tempFile, JSON.stringify(dictionary), 'utf8');
  
  // Use file-based fallback script
  const fallbackPath = path.join(__dirname, 'core', 'dictionary_save_fallback.py');
  const result = await runHybridScript(fallbackPath, [tempFile]);
  
  return result.trim() === 'true';
} catch (fallbackError) {
  console.error('[DICTIONARY] ❌ Improved fallback also failed:', fallbackError);
  return false;
}
'''
    
    try:
        with open('main_js_dictionary_fix.txt', 'w', encoding='utf-8') as f:
            f.write(fix_code)
        
        print("   ✅ Created main.js fix instructions: main_js_dictionary_fix.txt")
        return True
        
    except Exception as e:
        print(f"   ❌ Failed to create fix instructions: {e}")
        return False

def create_error_handling_enhancement():
    """Create enhanced error handling for the dictionary save operation"""
    print("\n🔧 CREATING ENHANCED ERROR HANDLING")
    print("=" * 50)
    
    error_handling_code = '''
// PRODUCTION FIX: Enhanced error handling for unified_database.js saveDictionary method
// Add this error handling wrapper around the saveDictionary method:

async saveDictionary(dictionary) {
    try {
        console.log('[UNIFIED_DB] Starting dictionary save operation...');
        
        // Validate input
        if (!dictionary || typeof dictionary !== 'object') {
            throw new Error('Invalid dictionary data provided');
        }
        
        const queries = [];
        
        // Clear existing data with error handling
        try {
            queries.push({ sql: 'DELETE FROM dictionary_items', params: [] });
            queries.push({ sql: 'DELETE FROM dictionary_sections', params: [] });
        } catch (clearError) {
            console.error('[UNIFIED_DB] Error preparing clear queries:', clearError);
            throw new Error(`Failed to prepare clear operations: ${clearError.message}`);
        }
        
        // Process sections with detailed error reporting
        for (const [sectionName, sectionData] of Object.entries(dictionary)) {
            try {
                queries.push({
                    sql: 'INSERT INTO dictionary_sections (section_name) VALUES (?)',
                    params: [sectionName]
                });
                
                if (sectionData.items) {
                    for (const [itemName, itemData] of Object.entries(sectionData.items)) {
                        try {
                            queries.push({
                                sql: `INSERT OR REPLACE INTO dictionary_items
                                      (section_id, item_name, standard_key, format_type, value_format,
                                       include_in_report, include_new, include_increase, include_decrease, 
                                       include_removed, include_no_change, is_fixed, validation_rules)
                                      VALUES ((SELECT id FROM dictionary_sections WHERE section_name = ?),
                                              ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                                params: [
                                    sectionName, itemName, itemData.standardized_name || itemName,
                                    itemData.format || 'text', itemData.value_format || 'text',
                                    itemData.include_in_report ? 1 : 0,
                                    itemData.include_new !== undefined ? (itemData.include_new ? 1 : 0) : 1,
                                    itemData.include_increase !== undefined ? (itemData.include_increase ? 1 : 0) : 1,
                                    itemData.include_decrease !== undefined ? (itemData.include_decrease ? 1 : 0) : 1,
                                    itemData.include_removed !== undefined ? (itemData.include_removed ? 1 : 0) : 1,
                                    itemData.include_no_change !== undefined ? (itemData.include_no_change ? 1 : 0) : 0,
                                    itemData.is_fixed ? 1 : 0,
                                    JSON.stringify(itemData.validation_rules || {})
                                ]
                            });
                        } catch (itemError) {
                            console.error(`[UNIFIED_DB] Error processing item ${itemName} in section ${sectionName}:`, itemError);
                            throw new Error(`Failed to process item ${itemName}: ${itemError.message}`);
                        }
                    }
                }
            } catch (sectionError) {
                console.error(`[UNIFIED_DB] Error processing section ${sectionName}:`, sectionError);
                throw new Error(`Failed to process section ${sectionName}: ${sectionError.message}`);
            }
        }
        
        // Execute transaction with detailed error reporting
        try {
            const result = await this.executeTransaction(queries);
            console.log(`[UNIFIED_DB] ✅ Dictionary saved successfully: ${queries.length} operations`);
            return result;
        } catch (transactionError) {
            console.error('[UNIFIED_DB] Transaction failed:', transactionError);
            throw new Error(`Database transaction failed: ${transactionError.message}`);
        }
        
    } catch (error) {
        console.error('[UNIFIED_DB] ❌ Dictionary save operation failed:', error);
        throw error; // Re-throw to trigger fallback
    }
}
'''
    
    try:
        with open('unified_database_error_handling_fix.txt', 'w', encoding='utf-8') as f:
            f.write(error_handling_code)
        
        print("   ✅ Created error handling enhancement: unified_database_error_handling_fix.txt")
        return True
        
    except Exception as e:
        print(f"   ❌ Failed to create error handling enhancement: {e}")
        return False

def main():
    """Main function to diagnose and fix dictionary save issues"""
    print("🚀 CRITICAL DICTIONARY SAVE FIX")
    print("=" * 60)
    
    # Test the current save operation
    save_test_passed = test_dictionary_save_operation()
    
    # Create improved fallback mechanism
    fallback_created = create_improved_fallback_mechanism()
    
    # Create main.js fix instructions
    main_fix_created = create_main_js_fix()
    
    # Create error handling enhancement
    error_handling_created = create_error_handling_enhancement()
    
    print("\n" + "=" * 60)
    print("📊 FIX SUMMARY:")
    print(f"   Dictionary Save Test: {'✅ PASSED' if save_test_passed else '❌ FAILED'}")
    print(f"   Fallback Script Created: {'✅ YES' if fallback_created else '❌ NO'}")
    print(f"   Main.js Fix Created: {'✅ YES' if main_fix_created else '❌ NO'}")
    print(f"   Error Handling Created: {'✅ YES' if error_handling_created else '❌ NO'}")
    
    if save_test_passed and fallback_created and main_fix_created and error_handling_created:
        print("\n✅ ALL FIXES CREATED SUCCESSFULLY!")
        print("🎯 Next steps:")
        print("   1. Apply the main.js fix from main_js_dictionary_fix.txt")
        print("   2. Apply the error handling fix from unified_database_error_handling_fix.txt")
        print("   3. Test the Dictionary Manager save operation")
        return True
    else:
        print("\n⚠️ SOME FIXES NEED ATTENTION")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
