#!/usr/bin/env python3
"""
PRODUCTION FIX: Department Column Population in Loan Tracker
Fixes the department data population issue in In-house loans, External Loans, and Motor Vehicle Maintenance tables.
"""

import sqlite3
import os
import sys
from datetime import datetime

def get_database_path():
    """Get the correct database path"""
    possible_paths = [
        'payroll_audit.db',
        'data/templar_payroll_auditor.db',
        'data/payroll_audit.db',
        'templar_payroll_auditor.db'
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    
    return 'payroll_audit.db'  # Default

def get_employee_department_from_db(cursor, employee_id, session_id):
    """
    Get employee department from the employees table or extracted_items table
    
    Args:
        cursor: Database cursor
        employee_id: Employee ID
        session_id: Current session ID
        
    Returns:
        Department string or fallback value
    """
    try:
        # Method 1: Try to get from employees table
        cursor.execute("""
            SELECT department FROM employees 
            WHERE employee_id = ? AND session_id = ?
            AND department IS NOT NULL AND department != ''
            ORDER BY created_at DESC LIMIT 1
        """, (employee_id, session_id))
        
        result = cursor.fetchone()
        if result and result[0] and result[0].strip():
            dept = result[0].strip()
            if dept not in ['None', 'UNKNOWN', 'NULL']:
                return dept
        
        # Method 2: Try to get from extracted_items (PERSONAL DETAILS section)
        cursor.execute("""
            SELECT item_value FROM extracted_items 
            WHERE employee_id = ? AND session_id = ?
            AND section_name = 'PERSONAL DETAILS'
            AND item_label IN ('DEPARTMENT', 'DEPT', 'DIVISION', 'UNIT', 'MINISTRY', 'DIRECTORATE')
            AND item_value IS NOT NULL AND item_value != ''
            ORDER BY created_at DESC LIMIT 1
        """, (employee_id, session_id))
        
        result = cursor.fetchone()
        if result and result[0] and result[0].strip():
            dept = result[0].strip()
            if dept not in ['None', 'UNKNOWN', 'NULL']:
                return dept
        
        # Method 3: Try any session for this employee (fallback)
        cursor.execute("""
            SELECT department FROM employees 
            WHERE employee_id = ?
            AND department IS NOT NULL AND department != ''
            ORDER BY created_at DESC LIMIT 1
        """, (employee_id,))
        
        result = cursor.fetchone()
        if result and result[0] and result[0].strip():
            dept = result[0].strip()
            if dept not in ['None', 'UNKNOWN', 'NULL']:
                return f"{dept} (Previous Session)"
        
        # Method 4: Pattern-based fallback
        if employee_id.startswith('COP'):
            return 'POLICE DEPARTMENT'
        elif employee_id.startswith('MIN'):
            return 'MINISTRY DEPARTMENT'
        elif employee_id.startswith('PW'):
            return 'PUBLIC WORKS DEPARTMENT'
        else:
            return 'DEPARTMENT_NOT_SPECIFIED'
            
    except Exception as e:
        print(f"   ⚠️ Error getting department for {employee_id}: {e}")
        return 'DEPARTMENT_LOOKUP_ERROR'

def fix_in_house_loans_department(cursor, conn, session_id):
    """Fix department population in in_house_loans table"""
    print("\n🏠 FIXING IN-HOUSE LOANS DEPARTMENT DATA:")
    
    try:
        # Get all in-house loans with missing or incorrect department data
        cursor.execute("""
            SELECT id, employee_no, employee_name, department, loan_type, loan_amount
            FROM in_house_loans 
            WHERE source_session = ?
            AND (department IS NULL OR department = '' OR department = 'Department not specified' 
                 OR department = 'Unknown' OR department = 'UNKNOWN')
        """, (session_id,))
        
        loans_to_fix = cursor.fetchall()
        print(f"   📊 Found {len(loans_to_fix)} in-house loans with missing department data")
        
        fixed_count = 0
        for loan in loans_to_fix:
            loan_id, employee_id, employee_name, old_dept, loan_type, loan_amount = loan
            
            # Get proper department
            new_dept = get_employee_department_from_db(cursor, employee_id, session_id)
            
            # Update the record
            cursor.execute("""
                UPDATE in_house_loans 
                SET department = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (new_dept, loan_id))
            
            fixed_count += 1
            if fixed_count <= 5:  # Show first 5 fixes
                print(f"   ✅ {employee_id}: '{old_dept}' → '{new_dept}'")
        
        conn.commit()
        print(f"   🎯 Fixed {fixed_count} in-house loan records")
        return fixed_count
        
    except Exception as e:
        print(f"   ❌ Error fixing in-house loans: {e}")
        return 0

def fix_external_loans_department(cursor, conn, session_id):
    """Fix department population in external_loans table"""
    print("\n🏢 FIXING EXTERNAL LOANS DEPARTMENT DATA:")
    
    try:
        # Get all external loans with missing or incorrect department data
        cursor.execute("""
            SELECT id, employee_no, employee_name, department, loan_type, loan_amount
            FROM external_loans 
            WHERE source_session = ?
            AND (department IS NULL OR department = '' OR department = 'Department not specified' 
                 OR department = 'Unknown' OR department = 'UNKNOWN')
        """, (session_id,))
        
        loans_to_fix = cursor.fetchall()
        print(f"   📊 Found {len(loans_to_fix)} external loans with missing department data")
        
        fixed_count = 0
        for loan in loans_to_fix:
            loan_id, employee_id, employee_name, old_dept, loan_type, loan_amount = loan
            
            # Get proper department
            new_dept = get_employee_department_from_db(cursor, employee_id, session_id)
            
            # Update the record
            cursor.execute("""
                UPDATE external_loans 
                SET department = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (new_dept, loan_id))
            
            fixed_count += 1
            if fixed_count <= 5:  # Show first 5 fixes
                print(f"   ✅ {employee_id}: '{old_dept}' → '{new_dept}'")
        
        conn.commit()
        print(f"   🎯 Fixed {fixed_count} external loan records")
        return fixed_count
        
    except Exception as e:
        print(f"   ❌ Error fixing external loans: {e}")
        return 0

def fix_motor_vehicle_department(cursor, conn, session_id):
    """Fix department population in motor_vehicle_maintenance table"""
    print("\n🚗 FIXING MOTOR VEHICLE MAINTENANCE DEPARTMENT DATA:")
    
    try:
        # Get all motor vehicle records with missing or incorrect department data
        cursor.execute("""
            SELECT id, employee_no, employee_name, department, maintenance_amount
            FROM motor_vehicle_maintenance 
            WHERE source_session = ?
            AND (department IS NULL OR department = '' OR department = 'Department not specified' 
                 OR department = 'Unknown' OR department = 'UNKNOWN')
        """, (session_id,))
        
        records_to_fix = cursor.fetchall()
        print(f"   📊 Found {len(records_to_fix)} motor vehicle records with missing department data")
        
        fixed_count = 0
        for record in records_to_fix:
            record_id, employee_id, employee_name, old_dept, maintenance_amount = record
            
            # Get proper department
            new_dept = get_employee_department_from_db(cursor, employee_id, session_id)
            
            # Update the record
            cursor.execute("""
                UPDATE motor_vehicle_maintenance 
                SET department = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (new_dept, record_id))
            
            fixed_count += 1
            if fixed_count <= 5:  # Show first 5 fixes
                print(f"   ✅ {employee_id}: '{old_dept}' → '{new_dept}'")
        
        conn.commit()
        print(f"   🎯 Fixed {fixed_count} motor vehicle records")
        return fixed_count
        
    except Exception as e:
        print(f"   ❌ Error fixing motor vehicle records: {e}")
        return 0

def update_future_population_logic(cursor, conn):
    """Create a view or function for future department lookups"""
    print("\n🔧 CREATING DEPARTMENT LOOKUP OPTIMIZATION:")
    
    try:
        # Create an index for faster department lookups
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_employees_dept_lookup 
            ON employees(employee_id, session_id, department)
        """)
        
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_extracted_items_dept_lookup 
            ON extracted_items(employee_id, session_id, section_name, item_label)
        """)
        
        conn.commit()
        print("   ✅ Created performance indexes for department lookups")
        
        # Create a view for easy department access
        cursor.execute("""
            CREATE VIEW IF NOT EXISTS employee_departments AS
            SELECT DISTINCT
                e.employee_id,
                e.session_id,
                COALESCE(
                    e.department,
                    ei.item_value,
                    CASE 
                        WHEN e.employee_id LIKE 'COP%' THEN 'POLICE DEPARTMENT'
                        WHEN e.employee_id LIKE 'MIN%' THEN 'MINISTRY DEPARTMENT'
                        WHEN e.employee_id LIKE 'PW%' THEN 'PUBLIC WORKS DEPARTMENT'
                        ELSE 'DEPARTMENT_NOT_SPECIFIED'
                    END
                ) as department
            FROM employees e
            LEFT JOIN extracted_items ei ON e.employee_id = ei.employee_id 
                AND e.session_id = ei.session_id
                AND ei.section_name = 'PERSONAL DETAILS'
                AND ei.item_label = 'DEPARTMENT'
            WHERE e.employee_id IS NOT NULL
        """)
        
        conn.commit()
        print("   ✅ Created employee_departments view for future lookups")
        return True
        
    except Exception as e:
        print(f"   ⚠️ Error creating optimization structures: {e}")
        return False

def verify_department_fixes(cursor, session_id):
    """Verify that department data has been properly fixed"""
    print("\n🔍 VERIFYING DEPARTMENT FIXES:")
    
    try:
        # Check in-house loans
        cursor.execute("""
            SELECT COUNT(*) as total,
                   SUM(CASE WHEN department IS NULL OR department = '' OR 
                            department = 'Department not specified' OR department = 'Unknown' 
                       THEN 1 ELSE 0 END) as missing
            FROM in_house_loans WHERE source_session = ?
        """, (session_id,))
        
        in_house_result = cursor.fetchone()
        in_house_total, in_house_missing = in_house_result
        
        # Check external loans
        cursor.execute("""
            SELECT COUNT(*) as total,
                   SUM(CASE WHEN department IS NULL OR department = '' OR 
                            department = 'Department not specified' OR department = 'Unknown' 
                       THEN 1 ELSE 0 END) as missing
            FROM external_loans WHERE source_session = ?
        """, (session_id,))
        
        external_result = cursor.fetchone()
        external_total, external_missing = external_result
        
        # Check motor vehicle
        cursor.execute("""
            SELECT COUNT(*) as total,
                   SUM(CASE WHEN department IS NULL OR department = '' OR 
                            department = 'Department not specified' OR department = 'Unknown' 
                       THEN 1 ELSE 0 END) as missing
            FROM motor_vehicle_maintenance WHERE source_session = ?
        """, (session_id,))
        
        motor_result = cursor.fetchone()
        motor_total, motor_missing = motor_result
        
        print(f"   📊 In-house loans: {in_house_total - in_house_missing}/{in_house_total} have proper departments")
        print(f"   📊 External loans: {external_total - external_missing}/{external_total} have proper departments")
        print(f"   📊 Motor vehicle: {motor_total - motor_missing}/{motor_total} have proper departments")
        
        total_fixed = (in_house_total - in_house_missing) + (external_total - external_missing) + (motor_total - motor_missing)
        total_records = in_house_total + external_total + motor_total
        
        if total_records > 0:
            success_rate = (total_fixed / total_records) * 100
            print(f"   🎯 Overall success rate: {success_rate:.1f}% ({total_fixed}/{total_records})")
            
            if success_rate >= 95:
                print("   ✅ Department population fix SUCCESSFUL!")
                return True
            else:
                print("   ⚠️ Some records still have missing departments")
                return False
        else:
            print("   ℹ️ No loan records found for verification")
            return True
            
    except Exception as e:
        print(f"   ❌ Verification failed: {e}")
        return False

def main():
    """Main function to fix department population issues"""
    print("🚀 DEPARTMENT POPULATION FIX - PRODUCTION SOLUTION")
    print("=" * 60)
    
    # Get database path
    db_path = get_database_path()
    print(f"📁 Database: {db_path}")
    
    if not os.path.exists(db_path):
        print(f"❌ Database not found at: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get current session
        cursor.execute("SELECT session_id FROM payroll_sessions ORDER BY created_at DESC LIMIT 1")
        session_result = cursor.fetchone()
        
        if not session_result:
            print("❌ No payroll sessions found")
            return False
        
        session_id = session_result[0]
        print(f"🎯 Working with session: {session_id}")
        
        # Fix each loan table
        in_house_fixed = fix_in_house_loans_department(cursor, conn, session_id)
        external_fixed = fix_external_loans_department(cursor, conn, session_id)
        motor_fixed = fix_motor_vehicle_department(cursor, conn, session_id)
        
        # Create optimization structures
        optimization_created = update_future_population_logic(cursor, conn)
        
        # Verify fixes
        verification_passed = verify_department_fixes(cursor, session_id)
        
        conn.close()
        
        total_fixed = in_house_fixed + external_fixed + motor_fixed
        
        print(f"\n✅ DEPARTMENT POPULATION FIX COMPLETED!")
        print(f"🎯 Total records fixed: {total_fixed}")
        print(f"   - In-house loans: {in_house_fixed}")
        print(f"   - External loans: {external_fixed}")
        print(f"   - Motor vehicle: {motor_fixed}")
        
        if optimization_created:
            print("🚀 Performance optimizations created for future lookups")
        
        if verification_passed:
            print("✅ All department data properly populated!")
        else:
            print("⚠️ Some issues remain - manual review recommended")
        
        return verification_passed
        
    except Exception as e:
        print(f"❌ Fix failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
