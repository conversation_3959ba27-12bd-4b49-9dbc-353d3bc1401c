#!/usr/bin/env python3
"""
Debug Auto-Learning Data Flow - Comprehensive Investigation
"""

import sys
import os
import sqlite3
import json

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def debug_auto_learning_data_flow():
    """Debug the complete Auto-Learning data flow"""
    print("🔍 AUTO-LEARNING DATA FLOW INVESTIGATION")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Get current session
        print("\n1. 📊 SESSION IDENTIFICATION:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.session_manager import get_current_session_id
            
            current_session = get_current_session_id()
            print(f"   Current session: {current_session}")
        except Exception as e:
            print(f"   ❌ Could not get current session: {e}")
            return
        
        # 2. Check extracted data availability
        print("\n2. 📊 EXTRACTED DATA AVAILABILITY:")
        
        cursor.execute("SELECT COUNT(*) FROM extracted_data WHERE session_id = ? AND period_type = 'current'", (current_session,))
        current_count = cursor.fetchone()[0]
        print(f"   Current period records: {current_count}")
        
        cursor.execute("SELECT COUNT(*) FROM extracted_data WHERE session_id = ? AND period_type = 'previous'", (current_session,))
        previous_count = cursor.fetchone()[0]
        print(f"   Previous period records: {previous_count}")
        
        # 3. Test _load_extracted_data method directly
        print("\n3. 🔄 TESTING _load_extracted_data METHOD:")
        
        try:
            from core.phased_process_manager import PhasedProcessManager
            
            manager = PhasedProcessManager(debug_mode=True)
            manager.session_id = current_session
            
            print("   Testing current data loading...")
            current_data = manager._load_extracted_data('current')
            
            if current_data:
                print(f"   ✅ Current data loaded: {len(current_data)} employees")
                
                # Sample employee structure
                sample_emp = current_data[0]
                print(f"   Sample employee ID: {sample_emp['employee_id']}")
                print(f"   Sample employee name: {sample_emp['employee_name']}")
                print(f"   Sample sections: {list(sample_emp['sections'].keys())}")
                
                # Count total items
                total_items = 0
                for emp in current_data:
                    for section in emp['sections'].values():
                        total_items += len(section)
                print(f"   Total items across all employees: {total_items}")
                
            else:
                print("   ❌ Current data returned None/empty")
                
        except Exception as e:
            print(f"   ❌ _load_extracted_data test failed: {e}")
            import traceback
            traceback.print_exc()
        
        # 4. Test Auto-Learning phase execution
        print("\n4. 🔄 TESTING AUTO-LEARNING PHASE EXECUTION:")
        
        try:
            # Clear existing results for clean test
            cursor.execute("DELETE FROM auto_learning_results WHERE session_id = ?", (current_session,))
            conn.commit()
            print("   Cleared existing auto learning results")
            
            # Test the actual phase method
            print("   Executing _phase_auto_learning...")
            
            options = {
                'report_name': 'Auto Learning Debug Test',
                'report_designation': 'Data Flow Investigation'
            }
            
            result = manager._phase_auto_learning(options)
            
            if result:
                print("   ✅ Auto-Learning phase executed successfully")
                
                # Check results
                cursor.execute("SELECT COUNT(*) FROM auto_learning_results WHERE session_id = ?", (current_session,))
                results_count = cursor.fetchone()[0]
                print(f"   Auto-learning results created: {results_count}")
                
                if results_count > 0:
                    cursor.execute("""
                        SELECT section_name, item_label, confidence_score, auto_approved 
                        FROM auto_learning_results 
                        WHERE session_id = ? 
                        LIMIT 10
                    """, (current_session,))
                    
                    sample_results = cursor.fetchall()
                    print("   Sample results:")
                    for result in sample_results:
                        print(f"     {result[0]}.{result[1]} (confidence: {result[2]}, auto-approved: {result[3]})")
                
            else:
                print("   ❌ Auto-Learning phase failed")
                
        except Exception as e:
            print(f"   ❌ Auto-Learning phase execution failed: {e}")
            import traceback
            traceback.print_exc()
        
        # 5. Check dictionary integration
        print("\n5. 📊 DICTIONARY INTEGRATION CHECK:")
        
        try:
            # Check if dictionary was updated
            cursor.execute("SELECT COUNT(*) FROM dictionary_items")
            dict_count = cursor.fetchone()[0]
            print(f"   Dictionary items count: {dict_count}")
            
            # Check for recent additions
            cursor.execute("""
                SELECT item_name, section_id, created_at 
                FROM dictionary_items 
                ORDER BY created_at DESC 
                LIMIT 5
            """)
            
            recent_items = cursor.fetchall()
            print("   Recent dictionary items:")
            for item in recent_items:
                print(f"     {item[0]} (section: {item[1]}, created: {item[2]})")
                
        except Exception as e:
            print(f"   ❌ Dictionary integration check failed: {e}")
        
        # 6. Final diagnosis
        print("\n6. 💡 DIAGNOSIS:")
        
        if current_count > 0 and current_data:
            print("   ✅ Data pipeline is working correctly")
            print("   ✅ _load_extracted_data method is functional")
            if result:
                print("   ✅ Auto-Learning phase execution is successful")
                print("   🎯 CONCLUSION: Auto-Learning system is working properly")
            else:
                print("   ❌ Auto-Learning phase execution failed")
                print("   🎯 ISSUE: Problem in Auto-Learning logic or dictionary integration")
        else:
            print("   ❌ Data pipeline has issues")
            if current_count == 0:
                print("   🎯 ISSUE: No extracted data for current session")
            else:
                print("   🎯 ISSUE: _load_extracted_data method not working")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during investigation: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_auto_learning_data_flow()
