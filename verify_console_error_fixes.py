#!/usr/bin/env python3
"""
Verify Console Error Fixes
Production verification of all implemented fixes
"""

import sys
import os
import json
import subprocess

def verify_console_error_fixes():
    """Verify that all console error fixes are working correctly"""
    print("🔍 VERIFYING CONSOLE ERROR FIXES")
    print("=" * 60)
    
    verification_results = {
        'tracker_population': False,
        'pre_reporting_data': False,
        'backend_commands': False,
        'error_handling': False
    }
    
    try:
        # 1. Verify Tracker Population Backend
        print("1. 📊 VERIFYING TRACKER POPULATION BACKEND:")
        
        result = subprocess.run([
            'python', 'bank_adviser_tracker_operations.py', 'populate_tables'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0 and result.stdout.strip():
            try:
                parsed = json.loads(result.stdout.strip())
                if parsed.get('success'):
                    print(f"   ✅ Backend working: {parsed.get('total', 0)} records")
                    verification_results['tracker_population'] = True
                else:
                    print(f"   ❌ Backend failed: {parsed.get('error')}")
            except json.JSONDecodeError:
                print("   ❌ Invalid JSON response")
        else:
            print("   ❌ Command execution failed")
        
        # 2. Verify Pre-reporting Data Backend
        print("\n2. 📊 VERIFYING PRE-REPORTING DATA BACKEND:")
        
        result = subprocess.run([
            'python', 'core/phased_process_manager.py', 'get-latest-pre-reporting-data'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0 and result.stdout.strip():
            try:
                parsed = json.loads(result.stdout.strip())
                if parsed.get('success'):
                    data_count = len(parsed.get('data', []))
                    print(f"   ✅ Backend working: {data_count} data items")
                    verification_results['pre_reporting_data'] = True
                else:
                    print(f"   ❌ Backend failed: {parsed.get('error')}")
            except json.JSONDecodeError:
                print("   ❌ Invalid JSON response")
        else:
            print("   ❌ Command execution failed")
        
        # 3. Verify Backend Commands Work Correctly
        print("\n3. 📊 VERIFYING BACKEND COMMAND RELIABILITY:")
        
        # Test multiple calls to ensure consistency
        success_count = 0
        total_tests = 3
        
        for i in range(total_tests):
            result = subprocess.run([
                'python', 'core/phased_process_manager.py', 'get-latest-pre-reporting-data'
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0 and result.stdout.strip():
                try:
                    parsed = json.loads(result.stdout.strip())
                    if parsed.get('success'):
                        success_count += 1
                except:
                    pass
        
        if success_count == total_tests:
            print(f"   ✅ Backend commands reliable: {success_count}/{total_tests} successful")
            verification_results['backend_commands'] = True
        else:
            print(f"   ❌ Backend commands unreliable: {success_count}/{total_tests} successful")
        
        # 4. Verify Error Handling Improvements
        print("\n4. 📊 VERIFYING ERROR HANDLING IMPROVEMENTS:")
        
        # Check if the frontend files have been updated
        frontend_fixes = []
        
        if os.path.exists('renderer.js'):
            with open('renderer.js', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for production fixes
            if 'PRODUCTION FIX: Proper success/warning/error distinction' in content:
                frontend_fixes.append('Tracker population error handling')
            
            if 'PRODUCTION FIX: Proper response validation and error handling' in content:
                frontend_fixes.append('Pre-reporting error handling')
            
            if 'normal for sessions without tracker data' in content:
                frontend_fixes.append('Empty state handling')
        
        if len(frontend_fixes) >= 2:
            print(f"   ✅ Frontend fixes applied: {', '.join(frontend_fixes)}")
            verification_results['error_handling'] = True
        else:
            print(f"   ❌ Frontend fixes incomplete: {', '.join(frontend_fixes)}")
        
        # 5. Test Database Connectivity
        print("\n5. 📊 VERIFYING DATABASE CONNECTIVITY:")
        
        try:
            import sqlite3
            
            db_path = r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db"
            if os.path.exists(db_path):
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # Test basic queries
                cursor.execute("SELECT COUNT(*) FROM comparison_results")
                comparison_count = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM tracker_results")
                tracker_count = cursor.fetchone()[0]
                
                conn.close()
                
                print(f"   ✅ Database accessible: {comparison_count} comparison, {tracker_count} tracker records")
            else:
                print("   ❌ Database not found")
        
        except Exception as e:
            print(f"   ❌ Database connectivity failed: {e}")
        
        # 6. Overall Verification Summary
        print("\n6. 🎯 VERIFICATION SUMMARY:")
        print("   " + "=" * 50)
        
        total_checks = len(verification_results)
        passed_checks = sum(verification_results.values())
        
        print(f"   📊 Checks passed: {passed_checks}/{total_checks}")
        
        for check, passed in verification_results.items():
            status = "✅" if passed else "❌"
            print(f"   {status} {check.replace('_', ' ').title()}")
        
        if passed_checks == total_checks:
            print("\n   🎉 ALL FIXES VERIFIED SUCCESSFULLY")
            print("   ✅ Console errors should be resolved")
            print("   ✅ Application should work without error messages")
        else:
            print(f"\n   ⚠️ {total_checks - passed_checks} ISSUES REMAINING")
            print("   🔍 Additional fixes may be needed")
        
        # 7. Next Steps
        print("\n7. 📋 NEXT STEPS:")
        print("   " + "=" * 50)
        
        if passed_checks == total_checks:
            print("   1. Restart the Electron application")
            print("   2. Open browser developer tools (F12)")
            print("   3. Navigate to Auto-Learning tab")
            print("   4. Verify no console errors appear")
            print("   5. Test tracker population and pre-reporting functionality")
        else:
            print("   1. Review failed verification checks above")
            print("   2. Apply additional fixes as needed")
            print("   3. Re-run this verification script")
        
        return verification_results
        
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        import traceback
        traceback.print_exc()
        return verification_results

if __name__ == "__main__":
    verify_console_error_fixes()
