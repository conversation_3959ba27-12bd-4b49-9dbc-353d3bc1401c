#!/usr/bin/env python3
"""
Root Cause Analysis for Console Errors
Production-ready analysis to identify exact issues
"""

import sys
import os
import sqlite3
import json
import subprocess

def analyze_console_errors():
    """Analyze the root causes of console errors"""
    print("🔍 ROOT CAUSE ANALYSIS FOR CONSOLE ERRORS")
    print("=" * 60)
    
    issues = []
    solutions = []
    
    try:
        # 1. Analyze Tracker Population Issues
        print("1. 📊 ANALYZING TRACKER POPULATION ISSUES:")
        
        # Test the actual command that fails
        result = subprocess.run([
            'python', 'bank_adviser_tracker_operations.py', 'populate_tables'
        ], capture_output=True, text=True, timeout=30)
        
        print(f"   Return code: {result.returncode}")
        print(f"   STDOUT: '{result.stdout.strip()}'")
        print(f"   STDERR: '{result.stderr.strip()}'")
        
        if result.returncode == 0 and result.stdout.strip():
            try:
                parsed = json.loads(result.stdout.strip())
                if parsed.get('success'):
                    print("   ✅ Tracker population command works correctly")
                    print(f"   📊 Populated: {parsed.get('total', 0)} records")
                else:
                    issues.append("Tracker population returns success=False")
                    print(f"   ❌ Command failed: {parsed.get('error')}")
            except json.JSONDecodeError:
                issues.append("Tracker population returns invalid JSON")
                print("   ❌ Invalid JSON output")
        else:
            issues.append("Tracker population command fails")
            print("   ❌ Command execution failed")
        
        # 2. Analyze Pre-reporting Data Issues
        print("\n2. 📊 ANALYZING PRE-REPORTING DATA ISSUES:")
        
        result = subprocess.run([
            'python', 'core/phased_process_manager.py', 'get-latest-pre-reporting-data'
        ], capture_output=True, text=True, timeout=30)
        
        print(f"   Return code: {result.returncode}")
        print(f"   STDOUT: '{result.stdout.strip()}'")
        print(f"   STDERR: '{result.stderr.strip()}'")
        
        if result.returncode == 0 and result.stdout.strip():
            try:
                parsed = json.loads(result.stdout.strip())
                if parsed.get('success'):
                    print("   ✅ Pre-reporting command works correctly")
                    print(f"   📊 Data items: {len(parsed.get('data', []))}")
                else:
                    issues.append("Pre-reporting returns success=False")
                    print(f"   ❌ Command failed: {parsed.get('error')}")
            except json.JSONDecodeError:
                issues.append("Pre-reporting returns invalid JSON")
                print("   ❌ Invalid JSON output")
        else:
            issues.append("Pre-reporting command returns empty output")
            print("   ❌ Empty or failed command output")
        
        # 3. Check Database State
        print("\n3. 📊 CHECKING DATABASE STATE:")
        
        db_path = r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db"
        if os.path.exists(db_path):
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Check current session
            try:
                sys.path.append(os.path.dirname(__file__))
                from core.session_manager import get_current_session_id
                current_session = get_current_session_id()
                print(f"   Current session: {current_session}")
            except Exception as e:
                print(f"   ❌ Session manager error: {e}")
                cursor.execute("SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1")
                result = cursor.fetchone()
                current_session = result[0] if result else None
                print(f"   Latest session from DB: {current_session}")
            
            if current_session:
                # Check comparison results
                cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (current_session,))
                comparison_count = cursor.fetchone()[0]
                print(f"   Comparison results for session: {comparison_count}")
                
                # Check tracker results
                cursor.execute("SELECT COUNT(*) FROM tracker_results WHERE session_id = ?", (current_session,))
                tracker_count = cursor.fetchone()[0]
                print(f"   Tracker results for session: {tracker_count}")
                
                if comparison_count == 0:
                    issues.append("No comparison results for current session")
                    solutions.append("Run complete processing job to generate comparison data")
                
                if tracker_count == 0:
                    issues.append("No tracker results for current session")
                    solutions.append("Ensure tracker learning phase runs during processing")
            else:
                issues.append("No current session available")
                solutions.append("Fix session management system")
            
            conn.close()
        else:
            issues.append("Database not found")
            solutions.append("Verify database path and permissions")
        
        # 4. Check Frontend Error Handling
        print("\n4. 📊 ANALYZING FRONTEND ERROR HANDLING:")
        
        # Check renderer.js for error handling patterns
        if os.path.exists('renderer.js'):
            with open('renderer.js', 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Check populateTrackerTablesRedesigned function
            if 'populateTrackerTablesRedesigned' in content:
                print("   ✅ populateTrackerTablesRedesigned function found")
                
                # Check error handling
                if 'console.warn' in content and 'Tracker population issues' in content:
                    print("   ⚠️ Function logs warnings for non-success results")
                    issues.append("Frontend treats non-perfect results as warnings")
                    solutions.append("Improve frontend error handling to distinguish real errors from warnings")
            
            # Check loadPreReportingUIFromDatabase function
            if 'loadPreReportingUIFromDatabase' in content:
                print("   ✅ loadPreReportingUIFromDatabase function found")
                
                if 'No pre-reporting data found' in content:
                    print("   ⚠️ Function logs errors when no data found")
                    issues.append("Frontend treats empty data as error instead of normal state")
                    solutions.append("Improve frontend to handle empty data gracefully")
        
        # 5. Root Cause Summary
        print("\n5. 🎯 ROOT CAUSE SUMMARY:")
        print("   " + "=" * 50)
        
        if issues:
            print("   🔍 ISSUES IDENTIFIED:")
            for i, issue in enumerate(issues, 1):
                print(f"      {i}. {issue}")
            
            print("\n   💡 SOLUTIONS REQUIRED:")
            for i, solution in enumerate(solutions, 1):
                print(f"      {i}. {solution}")
        else:
            print("   ✅ No obvious issues found in backend commands")
            print("   🔍 Issues likely in frontend error handling or timing")
        
        # 6. Specific Fix Recommendations
        print("\n6. 🔧 SPECIFIC FIX RECOMMENDATIONS:")
        print("   " + "=" * 50)
        
        print("   A. TRACKER POPULATION FIX:")
        print("      • Command works but frontend treats warnings as errors")
        print("      • Fix: Update frontend to handle success with warnings properly")
        
        print("   B. PRE-REPORTING DATA FIX:")
        print("      • Command may be failing silently or returning empty data")
        print("      • Fix: Add proper error handling and empty state management")
        
        print("   C. FRONTEND ERROR HANDLING FIX:")
        print("      • Improve error vs warning distinction")
        print("      • Add proper empty state handling")
        print("      • Implement retry mechanisms for transient failures")
        
        return {
            'issues': issues,
            'solutions': solutions,
            'tracker_working': result.returncode == 0 and result.stdout.strip(),
            'prereporting_working': False  # Based on empty output observed
        }
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return {'error': str(e)}

if __name__ == "__main__":
    analyze_console_errors()
