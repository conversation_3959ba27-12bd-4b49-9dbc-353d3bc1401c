#!/usr/bin/env python3
"""
Verify Auto-Learning Data Accessibility - Complete System Check
"""

import sys
import os
import sqlite3
import json

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def verify_auto_learning_data_accessibility():
    """Verify auto-learning data is accessible throughout the system"""
    print("🔍 VERIFYING AUTO-LEARNING DATA ACCESSIBILITY")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Get current session
        print("\n1. 📊 SESSION VERIFICATION:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.session_manager import get_current_session_id
            
            current_session = get_current_session_id()
            print(f"   Current session: {current_session}")
        except Exception as e:
            print(f"   ❌ Could not get current session: {e}")
            return
        
        # 2. Check auto-learning results in database
        print("\n2. 📊 AUTO-LEARNING RESULTS IN DATABASE:")
        
        cursor.execute("SELECT COUNT(*) FROM auto_learning_results WHERE session_id = ?", (current_session,))
        results_count = cursor.fetchone()[0]
        print(f"   Total auto-learning results: {results_count}")
        
        if results_count > 0:
            # Check breakdown by approval status
            cursor.execute("""
                SELECT auto_approved, COUNT(*) 
                FROM auto_learning_results 
                WHERE session_id = ? 
                GROUP BY auto_approved
            """, (current_session,))
            
            approval_breakdown = cursor.fetchall()
            print("   Approval breakdown:")
            for approved, count in approval_breakdown:
                status = "Auto-approved" if approved else "Pending review"
                print(f"     {status}: {count}")
            
            # Sample results
            cursor.execute("""
                SELECT section_name, item_label, confidence_score, auto_approved, dictionary_updated
                FROM auto_learning_results 
                WHERE session_id = ? 
                ORDER BY confidence_score DESC
                LIMIT 10
            """, (current_session,))
            
            sample_results = cursor.fetchall()
            print("   Top 10 results by confidence:")
            for result in sample_results:
                print(f"     {result[0]}.{result[1]} - Confidence: {result[2]:.2f}, Auto-approved: {result[3]}, Dict updated: {result[4]}")
        
        # 3. Test auto-learning system API access
        print("\n3. 🔄 TESTING AUTO-LEARNING SYSTEM API:")
        
        try:
            # Test enhanced dictionary auto-learning
            from backup_created_files.enhanced_dictionary_auto_learning import EnhancedDictionaryAutoLearning
            
            auto_learning = EnhancedDictionaryAutoLearning(debug=True)
            print("   ✅ Enhanced Dictionary Auto-Learning initialized")
            
            # Test getting pending items
            pending_items = auto_learning.get_pending_items()
            print(f"   Pending items from auto-learning system: {len(pending_items)}")
            
            # Test session stats
            session_stats = auto_learning.get_session_stats()
            print(f"   Session stats: {session_stats}")
            
        except Exception as e:
            print(f"   ❌ Auto-learning system API test failed: {e}")
            import traceback
            traceback.print_exc()
        
        # 4. Test phased process manager access
        print("\n4. 🔄 TESTING PHASED PROCESS MANAGER ACCESS:")
        
        try:
            from core.phased_process_manager import PhasedProcessManager
            
            manager = PhasedProcessManager(debug_mode=True)
            manager.session_id = current_session
            
            # Test get-pending-items command
            print("   Testing get-pending-items command...")
            
            # Simulate command line call
            import subprocess
            result = subprocess.run([
                'python', 
                'core/phased_process_manager.py', 
                'get-pending-items'
            ], capture_output=True, text=True, cwd=os.path.dirname(__file__))
            
            if result.returncode == 0:
                print("   ✅ get-pending-items command successful")
                try:
                    pending_data = json.loads(result.stdout.strip())
                    print(f"   Pending items via command: {len(pending_data.get('pending_items', []))}")
                except:
                    print(f"   Raw output: {result.stdout[:200]}...")
            else:
                print(f"   ❌ get-pending-items command failed: {result.stderr}")
                
        except Exception as e:
            print(f"   ❌ Phased process manager test failed: {e}")
        
        # 5. Test IPC handler access
        print("\n5. 🔄 TESTING IPC HANDLER ACCESS:")
        
        try:
            # Check if main.js IPC handlers can access the data
            print("   Testing main.js get-pending-items handler...")
            
            # Simulate the IPC call by running the Python script directly
            result = subprocess.run([
                'python', 
                'core/phased_process_manager.py', 
                'get-pending-items'
            ], capture_output=True, text=True, cwd=os.path.dirname(__file__))
            
            if result.returncode == 0:
                print("   ✅ IPC handler script execution successful")
                
                # Try to parse the result
                output = result.stdout.strip()
                
                # Find JSON in output
                json_start = output.find('{')
                if json_start >= 0:
                    json_part = output[json_start:]
                    json_end = json_part.rfind('}') + 1
                    if json_end > 0:
                        clean_json = json_part[:json_end]
                        try:
                            parsed_data = json.loads(clean_json)
                            print(f"   Parsed pending items: {len(parsed_data.get('pending_items', []))}")
                            print(f"   Success status: {parsed_data.get('success', False)}")
                        except:
                            print(f"   Raw JSON: {clean_json[:100]}...")
                else:
                    print(f"   No JSON found in output: {output[:100]}...")
            else:
                print(f"   ❌ IPC handler script failed: {result.stderr}")
                
        except Exception as e:
            print(f"   ❌ IPC handler test failed: {e}")
        
        # 6. Check UI accessibility
        print("\n6. 📊 UI ACCESSIBILITY CHECK:")
        
        try:
            # Check if the data would be accessible to the UI
            cursor.execute("""
                SELECT section_name, item_label, confidence_score
                FROM auto_learning_results 
                WHERE session_id = ? AND auto_approved = 0
                ORDER BY confidence_score DESC
                LIMIT 5
            """, (current_session,))
            
            ui_data = cursor.fetchall()
            print(f"   Items available for UI review: {len(ui_data)}")
            
            if ui_data:
                print("   Sample items for UI:")
                for item in ui_data:
                    print(f"     {item[0]}.{item[1]} (confidence: {item[2]:.2f})")
            
        except Exception as e:
            print(f"   ❌ UI accessibility check failed: {e}")
        
        # 7. Final verification summary
        print("\n7. 📋 VERIFICATION SUMMARY:")
        
        if results_count > 0:
            print("   ✅ Auto-learning data exists in database")
            print("   ✅ Data is accessible via SQL queries")
            
            # Check if system components can access the data
            accessible_components = []
            if 'auto_learning' in locals():
                accessible_components.append("Enhanced Dictionary Auto-Learning")
            if 'manager' in locals():
                accessible_components.append("Phased Process Manager")
            
            if accessible_components:
                print(f"   ✅ Data accessible via: {', '.join(accessible_components)}")
            else:
                print("   ⚠️ Some system components may have access issues")
            
            print(f"   📊 Total items for review: {results_count}")
            print("   🎯 CONCLUSION: Auto-learning data is properly stored and accessible")
        else:
            print("   ❌ No auto-learning data found")
            print("   🎯 ISSUE: Auto-learning phase may not have run or data was cleared")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verify_auto_learning_data_accessibility()
