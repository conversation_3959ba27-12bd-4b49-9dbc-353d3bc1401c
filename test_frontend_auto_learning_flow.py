#!/usr/bin/env python3
"""
Test Frontend Auto-Learning Data Flow
Comprehensive test to identify where the data flow is breaking
"""

import sys
import os
import subprocess
import json
import time

def test_frontend_auto_learning_flow():
    """Test the complete frontend auto-learning data flow"""
    print("🔍 TESTING FRONTEND AUTO-LEARNING DATA FLOW")
    print("=" * 60)
    
    try:
        # 1. Test the exact API call that the frontend makes
        print("1. 🔄 TESTING API CALL (as frontend does):")
        
        python_path = os.path.join(os.path.dirname(__file__), 'core', 'phased_process_manager.py')
        
        result = subprocess.run(
            ['python', python_path, 'get-pending-items'],
            capture_output=True,
            text=True,
            cwd=os.path.dirname(__file__)
        )
        
        print(f"   📊 Return code: {result.returncode}")
        print(f"   📊 STDOUT length: {len(result.stdout)}")
        
        if result.returncode != 0:
            print(f"   ❌ API call failed: {result.stderr}")
            return
        
        if not result.stdout:
            print("   ❌ No output from API")
            return
        
        # 2. Test JSON parsing (as main.js does)
        print("\n2. 📋 TESTING JSON PARSING:")
        
        try:
            # Clean the result as main.js does
            clean_result = result.stdout.strip()
            
            # Find the first { to locate JSON start
            json_start = clean_result.find('{')
            if json_start > 0:
                clean_result = clean_result[json_start:]
            
            # Find the last } to locate JSON end
            json_end = clean_result.rfind('}')
            if json_end != -1 and json_end < len(clean_result) - 1:
                clean_result = clean_result[:json_end + 1]
            
            # Parse JSON
            parsed_result = json.loads(clean_result)
            
            print(f"   ✅ JSON parsing successful")
            print(f"   📊 Success: {parsed_result.get('success')}")
            print(f"   📊 Count: {parsed_result.get('count', 0)}")
            
            if not parsed_result.get('success'):
                print(f"   ❌ API returned error: {parsed_result.get('error')}")
                return
            
            data = parsed_result.get('data', [])
            print(f"   📊 Data items: {len(data)}")
            
            if len(data) == 0:
                print("   ❌ No data items returned")
                return
            
            # 3. Test frontend data structure mapping
            print("\n3. 🔄 TESTING FRONTEND DATA MAPPING:")
            
            # Simulate the frontend data mapping logic
            pending_items = data  # This is what result.data would be
            
            print(f"   📊 Raw items before mapping: {len(pending_items)}")
            
            # Apply the same mapping logic as the frontend
            mapped_items = []
            for index, item in enumerate(pending_items):
                if not item or not isinstance(item, dict):
                    print(f"   ⚠️ Invalid item at index {index}: {item}")
                    continue
                
                mapped_item = {
                    'id': item.get('id') or f"item_{index}_{int(time.time())}",
                    'item_name': item.get('item_label') or item.get('item_name') or item.get('label') or 'Unknown',
                    'section': item.get('section_name') or item.get('section') or item.get('suggested_section') or 'EARNINGS',
                    'value': item.get('value') or 'N/A',
                    'confidence': item.get('confidence_score') if isinstance(item.get('confidence_score'), (int, float)) else 
                                 (item.get('confidence') if isinstance(item.get('confidence'), (int, float)) else 0),
                    'source': item.get('source') or 'Auto-detected',
                    'auto_approved': item.get('auto_approved') or False,
                    'is_new_item': item.get('is_new_item', True),
                    'status': item.get('status') or 'pending_approval',
                    'timestamp': item.get('created_at') or item.get('timestamp') or time.strftime('%Y-%m-%d %H:%M:%S')
                }
                
                mapped_items.append(mapped_item)
            
            print(f"   📊 Mapped items: {len(mapped_items)}")
            
            if len(mapped_items) > 0:
                print("   📋 Sample mapped items:")
                for i, item in enumerate(mapped_items[:3]):
                    print(f"     {i+1}. {item['item_name']} → {item['section']} (confidence: {item['confidence']})")
                
                print("   ✅ Frontend data mapping successful")
            else:
                print("   ❌ Frontend data mapping failed - no items after mapping")
                
                # Debug the first few raw items
                print("   🔍 Debugging raw items:")
                for i, item in enumerate(data[:3]):
                    print(f"     Raw item {i+1}: {item}")
                return
            
            # 4. Test the complete frontend simulation
            print("\n4. 🎨 TESTING COMPLETE FRONTEND SIMULATION:")
            
            # Simulate the complete frontend flow
            frontend_result = {
                'success': parsed_result.get('success'),
                'data': data,
                'count': parsed_result.get('count', 0)
            }
            
            # Simulate loadPendingItems function logic
            if isinstance(frontend_result, list):
                pending_items_final = frontend_result
            else:
                pending_items_final = frontend_result.get('data', [])
            
            print(f"   📊 Final pending items count: {len(pending_items_final)}")
            
            # Simulate the stats calculation
            stats = {
                'pending_count': len(pending_items_final),
                'auto_added_today': len([item for item in pending_items_final if item.get('auto_approved')]),
                'learning_accuracy': '100%',
                'status': 'Ready'
            }
            
            print(f"   📊 Calculated stats: {stats}")
            
            # 5. Check for potential issues
            print("\n5. 🔍 CHECKING FOR POTENTIAL ISSUES:")
            
            # Check if items have required fields
            missing_fields = []
            for item in data[:5]:  # Check first 5 items
                required_fields = ['id', 'item_label', 'section_name', 'confidence_score']
                for field in required_fields:
                    if field not in item:
                        missing_fields.append(f"Missing {field} in item {item.get('id', 'unknown')}")
            
            if missing_fields:
                print("   ⚠️ Missing required fields:")
                for missing in missing_fields:
                    print(f"     {missing}")
            else:
                print("   ✅ All required fields present")
            
            # Check confidence score format
            confidence_issues = []
            for item in data[:5]:
                confidence = item.get('confidence_score')
                if not isinstance(confidence, (int, float)):
                    confidence_issues.append(f"Item {item.get('id')}: confidence_score is {type(confidence)} ({confidence})")
            
            if confidence_issues:
                print("   ⚠️ Confidence score format issues:")
                for issue in confidence_issues:
                    print(f"     {issue}")
            else:
                print("   ✅ Confidence scores properly formatted")
            
            # 6. Generate test HTML to verify rendering
            print("\n6. 📄 GENERATING TEST HTML:")
            
            html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>Auto-Learning Test</title>
    <style>
        table {{ border-collapse: collapse; width: 100%; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        .confidence-high {{ background-color: #d4edda; }}
        .confidence-medium {{ background-color: #fff3cd; }}
        .confidence-low {{ background-color: #f8d7da; }}
    </style>
</head>
<body>
    <h1>Auto-Learning Test Results</h1>
    <p><strong>Total Items:</strong> {len(mapped_items)}</p>
    <p><strong>API Success:</strong> {parsed_result.get('success')}</p>
    <p><strong>Stats:</strong> {stats}</p>
    
    <table>
        <thead>
            <tr>
                <th>ID</th>
                <th>Item Name</th>
                <th>Section</th>
                <th>Confidence</th>
                <th>Status</th>
            </tr>
        </thead>
        <tbody>
"""
            
            for item in mapped_items[:10]:  # Show first 10 items
                confidence_class = 'confidence-high' if item['confidence'] > 0.8 else ('confidence-medium' if item['confidence'] > 0.5 else 'confidence-low')
                html_content += f"""
            <tr>
                <td>{item['id']}</td>
                <td>{item['item_name']}</td>
                <td>{item['section']}</td>
                <td class="{confidence_class}">{item['confidence']:.2f}</td>
                <td>{item['status']}</td>
            </tr>
"""
            
            html_content += """
        </tbody>
    </table>
</body>
</html>
"""
            
            with open('auto_learning_test_results.html', 'w') as f:
                f.write(html_content)
            
            print("   ✅ Test HTML generated: auto_learning_test_results.html")
            
            # 7. Summary
            print("\n7. 📋 SUMMARY:")
            print("   " + "=" * 50)
            
            if len(mapped_items) > 0:
                print("   ✅ COMPLETE DATA FLOW WORKING")
                print(f"   ✅ {len(mapped_items)} items successfully processed")
                print("   ✅ Data mapping logic working correctly")
                print("   ✅ All required fields present and properly formatted")
                print("")
                print("   🔍 ISSUE LIKELY IN:")
                print("   • Browser caching of JavaScript files")
                print("   • Electron app not reloading updated files")
                print("   • Frontend event listeners not properly attached")
                print("   • UI refresh mechanism not triggering")
                print("")
                print("   💡 RECOMMENDED ACTIONS:")
                print("   1. Hard refresh the browser (Ctrl+F5)")
                print("   2. Restart the Electron application")
                print("   3. Clear browser cache")
                print("   4. Check browser console for JavaScript errors")
            else:
                print("   ❌ DATA FLOW BROKEN")
                print("   🔍 Issue in data mapping logic")
            
        except json.JSONDecodeError as e:
            print(f"   ❌ JSON parsing failed: {e}")
            print(f"   📋 Raw output: {result.stdout[:500]}")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_frontend_auto_learning_flow()
