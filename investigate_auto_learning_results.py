#!/usr/bin/env python3
"""
Investigate Auto-Learning Results Table
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def investigate_auto_learning_results():
    """Investigate the auto_learning_results table"""
    print("🔍 AUTO-LEARNING RESULTS INVESTIGATION")
    print("=" * 50)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get current session
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.session_manager import get_current_session_id
            current_session = get_current_session_id()
            print(f"📋 Current session: {current_session}")
        except Exception as e:
            print(f"❌ Session manager error: {e}")
            cursor.execute("SELECT DISTINCT session_id FROM comparison_results ORDER BY session_id DESC LIMIT 1")
            result = cursor.fetchone()
            current_session = result[0] if result else None
            print(f"📋 Latest session from DB: {current_session}")
        
        # 1. Check auto_learning_results table structure
        print("\n1. 📊 AUTO_LEARNING_RESULTS TABLE STRUCTURE:")
        cursor.execute("PRAGMA table_info(auto_learning_results)")
        columns = cursor.fetchall()
        
        print("   📋 Table structure:")
        for col in columns:
            print(f"     {col[1]} ({col[2]}) - {'NOT NULL' if col[3] else 'NULL'}")
        
        # 2. Check total items
        cursor.execute("SELECT COUNT(*) FROM auto_learning_results")
        total_items = cursor.fetchone()[0]
        print(f"\n2. 📊 TOTAL AUTO-LEARNING RESULTS: {total_items}")
        
        # 3. Check items for current session
        if current_session:
            cursor.execute("SELECT COUNT(*) FROM auto_learning_results WHERE session_id = ?", (current_session,))
            session_items = cursor.fetchone()[0]
            print(f"\n3. 📊 ITEMS FOR CURRENT SESSION: {session_items}")
            
            if session_items > 0:
                # Show sample items from current session
                cursor.execute("""
                    SELECT section_name, item_label, confidence_score, auto_approved, dictionary_updated, created_at
                    FROM auto_learning_results 
                    WHERE session_id = ?
                    ORDER BY created_at DESC
                    LIMIT 10
                """, (current_session,))
                
                items = cursor.fetchall()
                print("\n   📋 Sample items from current session:")
                for section, item, confidence, auto_approved, dict_updated, created_at in items:
                    print(f"     {section}.{item} (confidence: {confidence}, auto_approved: {auto_approved}, dict_updated: {dict_updated})")
            else:
                print("\n   ❌ No items found for current session")
        
        # 4. Check recent sessions with auto-learning data
        print("\n4. 📊 RECENT SESSIONS WITH AUTO-LEARNING DATA:")
        cursor.execute("""
            SELECT session_id, COUNT(*) as count, MAX(created_at) as latest
            FROM auto_learning_results 
            GROUP BY session_id
            ORDER BY latest DESC
            LIMIT 5
        """)
        
        recent_sessions = cursor.fetchall()
        for session_id, count, latest in recent_sessions:
            print(f"   {session_id}: {count} items (latest: {latest})")
        
        # 5. Check what items are pending approval
        print("\n5. 🔍 CHECKING FOR PENDING APPROVAL ITEMS:")
        
        # Check if there are items that are not auto-approved and not dictionary updated
        cursor.execute("""
            SELECT COUNT(*) FROM auto_learning_results 
            WHERE auto_approved = 0 AND dictionary_updated = 0
        """)
        pending_count = cursor.fetchone()[0]
        print(f"   📊 Items pending approval (not auto-approved, not dict-updated): {pending_count}")
        
        if pending_count > 0:
            # Show some pending items
            cursor.execute("""
                SELECT session_id, section_name, item_label, confidence_score, created_at
                FROM auto_learning_results 
                WHERE auto_approved = 0 AND dictionary_updated = 0
                ORDER BY created_at DESC
                LIMIT 10
            """)
            
            pending_items = cursor.fetchall()
            print("\n   📋 Sample pending items:")
            for session_id, section, item, confidence, created_at in pending_items:
                print(f"     {session_id}: {section}.{item} (confidence: {confidence}, created: {created_at})")
        
        # 6. Check the auto_learning_items table relationship
        print("\n6. 🔄 CHECKING AUTO_LEARNING_ITEMS TABLE:")
        
        cursor.execute("SELECT COUNT(*) FROM auto_learning_items")
        items_count = cursor.fetchone()[0]
        print(f"   📊 Items in auto_learning_items table: {items_count}")
        
        if items_count == 0:
            print("   ❌ auto_learning_items table is empty")
            print("   💡 This explains why the UI shows no pending items")
            print("   🔍 The system is storing data in auto_learning_results but not auto_learning_items")
        
        # 7. Test the Enhanced Auto-Learning System
        print("\n7. 🤖 TESTING ENHANCED AUTO-LEARNING SYSTEM:")
        
        try:
            from backup_created_files.enhanced_dictionary_auto_learning import EnhancedDictionaryAutoLearning
            
            auto_learning = EnhancedDictionaryAutoLearning(debug=False)
            print("   ✅ Enhanced Auto-Learning system imported")
            
            # Test getting pending items
            pending_items = auto_learning.get_pending_items()
            print(f"   📊 Pending items from Enhanced Auto-Learning: {len(pending_items)}")
            
            # Test session stats
            session_stats = auto_learning.get_session_stats()
            print(f"   📊 Session stats: {session_stats}")
            
        except Exception as e:
            print(f"   ❌ Enhanced Auto-Learning system test failed: {e}")
        
        conn.close()
        
        # 8. Root Cause Analysis
        print("\n8. 🎯 ROOT CAUSE ANALYSIS:")
        print("   " + "=" * 40)
        
        if total_items > 0 and items_count == 0:
            print("   🔍 ISSUE IDENTIFIED:")
            print("   • Auto-Learning system is working and storing data in auto_learning_results")
            print("   • BUT: auto_learning_items table (used by UI) is empty")
            print("   • The UI reads from auto_learning_items, not auto_learning_results")
            print("   • This is a DATA FLOW DISCONNECT between processing and UI")
            
            print("\n   💡 SOLUTION NEEDED:")
            print("   • Either: Populate auto_learning_items from auto_learning_results")
            print("   • Or: Update UI to read from auto_learning_results")
            print("   • Or: Fix the Auto-Learning system to write to auto_learning_items")
        
        elif total_items == 0:
            print("   🔍 ISSUE IDENTIFIED:")
            print("   • No auto-learning data generated during processing")
            print("   • Auto-Learning system may not be triggered during extraction")
            
        else:
            print("   ✅ Data flow appears normal - investigate UI connectivity")
        
    except Exception as e:
        print(f"❌ Investigation failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    investigate_auto_learning_results()
